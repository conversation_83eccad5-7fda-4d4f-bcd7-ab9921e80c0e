{"rustc": 7868289081541623310, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-mint\", \"cuda\", \"cust_core\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 6418093726722172150, "path": 15180879785478055608, "deps": [[2819946551904607991, "num_rational", false, 5985183609095207161], [4462856585586636430, "simba", false, 3697301094392191413], [5157631553186200874, "num_traits", false, 4857534085093177022], [11394677342629719743, "nalgebra_macros", false, 6199649314956212618], [12319020793864570031, "num_complex", false, 3200402049261569971], [15677050387741058262, "approx", false, 15534233484353261460], [15826188163127377936, "matrixmultiply", false, 13239015261954619094], [17001665395952474378, "typenum", false, 10192193046281337573]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\nalgebra-86433f58970ad976\\dep-lib-nalgebra", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}