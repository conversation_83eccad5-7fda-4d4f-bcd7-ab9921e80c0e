{"rustc": 7868289081541623310, "features": "[\"std\", \"wide\"]", "declared_features": "[\"cordic\", \"cuda\", \"cuda_std\", \"cust_core\", \"decimal\", \"default\", \"fixed\", \"libm\", \"libm_force\", \"packed_simd\", \"partial_fixed_point_support\", \"rand\", \"rkyv\", \"rkyv-serialize\", \"serde\", \"serde_serialize\", \"std\", \"wide\"]", "target": 18314989904106682660, "profile": 6418093726722172150, "path": 13292842008380283976, "deps": [[5157631553186200874, "num_traits", false, 4857534085093177022], [11243818633362483251, "wide", false, 17960242611605599052], [12319020793864570031, "num_complex", false, 3200402049261569971], [15677050387741058262, "approx", false, 15534233484353261460], [17605717126308396068, "paste", false, 13040440031253527575]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\simba-f0d615ecd74fd51d\\dep-lib-simba", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}