#!/bin/bash

# PDF Rendering Engine - WebAssembly Build Script

set -e

echo "🔧 Building PDF Rendering Engine for WebAssembly..."

# Check if wasm-pack is installed
if ! command -v wasm-pack &> /dev/null; then
    echo "❌ wasm-pack is not installed. Installing..."
    curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
fi

# Check if wasm32-unknown-unknown target is installed
if ! rustup target list --installed | grep -q "wasm32-unknown-unknown"; then
    echo "📦 Installing wasm32-unknown-unknown target..."
    rustup target add wasm32-unknown-unknown
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf pkg/
rm -rf target/

# Set environment variables for optimization
export RUSTFLAGS="-C target-feature=+simd128 -C target-feature=+bulk-memory"

# Build for different targets
echo "🚀 Building for web (ES modules)..."
wasm-pack build --target web --out-dir pkg/web --release

echo "🚀 Building for bundler (webpack, etc.)..."
wasm-pack build --target bundler --out-dir pkg/bundler --release

echo "🚀 Building for Node.js..."
wasm-pack build --target nodejs --out-dir pkg/nodejs --release

# Optimize WASM files
if command -v wasm-opt &> /dev/null; then
    echo "⚡ Optimizing WASM files..."
    
    for pkg_dir in pkg/*/; do
        if [ -f "${pkg_dir}pdf_render_engine_bg.wasm" ]; then
            echo "Optimizing ${pkg_dir}pdf_render_engine_bg.wasm"
            wasm-opt -Oz --enable-mutable-globals "${pkg_dir}pdf_render_engine_bg.wasm" \
                -o "${pkg_dir}pdf_render_engine_bg.wasm"
        fi
    done
else
    echo "⚠️  wasm-opt not found. Install binaryen for better optimization."
fi

# Generate TypeScript definitions
echo "📝 Generating TypeScript definitions..."
for pkg_dir in pkg/*/; do
    if [ -f "${pkg_dir}pdf_render_engine.d.ts" ]; then
        echo "TypeScript definitions available in ${pkg_dir}"
    fi
done

# Create a unified package.json for npm publishing
echo "📦 Creating unified package..."
cat > pkg/package.json << EOF
{
  "name": "pdf-render-engine",
  "version": "0.1.0",
  "description": "High-performance PDF rendering engine with WebAssembly",
  "main": "bundler/pdf_render_engine.js",
  "module": "web/pdf_render_engine.js",
  "types": "bundler/pdf_render_engine.d.ts",
  "files": [
    "bundler/",
    "web/",
    "nodejs/",
    "README.md"
  ],
  "keywords": [
    "pdf",
    "rendering",
    "webassembly",
    "wasm",
    "rust"
  ],
  "author": "PDF Render Engine Team",
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/your-org/pdf-render-engine"
  },
  "engines": {
    "node": ">=14.0.0"
  }
}
EOF

# Create README for the package
cat > pkg/README.md << EOF
# PDF Rendering Engine

A high-performance PDF rendering engine built with Rust and WebAssembly.

## Features

- 🚀 High-performance PDF parsing and rendering
- 🔧 Hybrid parser support (PDFium + pure Rust fallbacks)
- 🎨 Multiple rendering backends (Canvas2D, WebGL)
- 💾 Intelligent caching system
- 🔍 Text extraction and search
- 📱 Cross-platform support (Web, Node.js)

## Installation

\`\`\`bash
npm install pdf-render-engine
\`\`\`

## Usage

### Web (ES Modules)

\`\`\`javascript
import init, { WasmPDFEngine } from 'pdf-render-engine/web/pdf_render_engine.js';

async function loadPDF() {
    await init();
    
    const engine = new WasmPDFEngine();
    const pdfData = new Uint8Array(/* your PDF data */);
    
    const docInfo = await engine.load_document(pdfData);
    console.log('Pages:', docInfo.page_count);
    
    await engine.render_page(1, 1.0, 'canvas-id');
}
\`\`\`

### Bundler (Webpack, Vite, etc.)

\`\`\`javascript
import init, { WasmPDFEngine } from 'pdf-render-engine';

// Same usage as above
\`\`\`

### Node.js

\`\`\`javascript
const { WasmPDFEngine } = require('pdf-render-engine/nodejs/pdf_render_engine.js');

// Node.js usage (without canvas rendering)
\`\`\`

## API Reference

### WasmPDFEngine

#### Methods

- \`new WasmPDFEngine()\` - Create a new PDF engine instance
- \`load_document(data: Uint8Array)\` - Load a PDF document
- \`get_page_count()\` - Get the number of pages
- \`render_page(pageNum: number, scale: number, canvasId: string)\` - Render a page
- \`extract_text(pageNum: number)\` - Extract text from a page
- \`search_text(query: string)\` - Search for text in the document

## Browser Support

- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## License

MIT
EOF

# Display build summary
echo ""
echo "✅ Build completed successfully!"
echo ""
echo "📊 Build Summary:"
echo "  - Web package: pkg/web/"
echo "  - Bundler package: pkg/bundler/"
echo "  - Node.js package: pkg/nodejs/"
echo "  - Unified package: pkg/"
echo ""

# Display file sizes
echo "📏 File Sizes:"
for pkg_dir in pkg/*/; do
    if [ -f "${pkg_dir}pdf_render_engine_bg.wasm" ]; then
        size=$(du -h "${pkg_dir}pdf_render_engine_bg.wasm" | cut -f1)
        echo "  - $(basename "$pkg_dir"): $size"
    fi
done

echo ""
echo "🚀 Ready to use! Check the examples in the pkg/ directory."
echo ""
echo "To test locally:"
echo "  cd pkg && npm link"
echo "  # In your project: npm link pdf-render-engine"
echo ""
echo "To publish:"
echo "  cd pkg && npm publish"
