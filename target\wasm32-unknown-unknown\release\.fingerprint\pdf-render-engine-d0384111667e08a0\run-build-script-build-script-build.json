{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18072840692915945376, "build_script_build", false, 2391757794471210326]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\pdf-render-engine-d0384111667e08a0\\output", "paths": ["build.rs", "Cargo.toml"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}