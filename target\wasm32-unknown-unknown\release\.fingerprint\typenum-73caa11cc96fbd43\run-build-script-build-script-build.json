{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17001665395952474378, "build_script_build", false, 15842469973257243416]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\typenum-73caa11cc96fbd43\\output", "paths": ["tests"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}