{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14851956875005785803, "build_script_build", false, 10275722296965699676]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\serde-bbd72d86d9949fa5\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}