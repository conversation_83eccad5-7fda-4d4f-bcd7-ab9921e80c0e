{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"futures-core\", \"futures-core-03-stream\", \"std\"]", "target": 4429042720284741532, "profile": 16333205938936660957, "path": 14084935036843235177, "deps": [[1267401389414215502, "js_sys", false, 9481063980748426554], [3722963349756955755, "once_cell", false, 15574632292824914997], [7843059260364151289, "cfg_if", false, 10459516428561978112], [17362525766049117937, "wasm_bindgen", false, 1611265920349769740]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\wasm-bindgen-futures-a66dd3b7232397ad\\dep-lib-wasm_bindgen_futures", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}