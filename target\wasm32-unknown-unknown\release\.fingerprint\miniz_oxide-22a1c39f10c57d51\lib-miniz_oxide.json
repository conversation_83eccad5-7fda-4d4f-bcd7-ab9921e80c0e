{"rustc": 7868289081541623310, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 912382958768210079, "path": 9124921947395243039, "deps": [[4018467389006652250, "simd_adler32", false, 15883599475675365607], [7911289239703230891, "adler2", false, 14205833182221666563]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\miniz_oxide-22a1c39f10c57d51\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}