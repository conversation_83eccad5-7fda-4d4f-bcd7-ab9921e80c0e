{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"std\"]", "target": 340870475748378612, "profile": 6418093726722172150, "path": 10978592568007297832, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\adler32-7d7e2b18bcdf187c\\dep-lib-adler32", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}