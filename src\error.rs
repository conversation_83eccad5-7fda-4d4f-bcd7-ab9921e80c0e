use thiserror::Error;
use wasm_bindgen::prelude::*;

/// PDF engine error types
#[derive(Erro<PERSON>, Debug)]
pub enum PDFError {
    #[error("Failed to parse PDF document: {0}")]
    ParseError(String),
    
    #[error("Failed to render page {page}: {reason}")]
    RenderError { page: u32, reason: String },
    
    #[error("Invalid page number: {page} (document has {total_pages} pages)")]
    InvalidPageNumber { page: u32, total_pages: u32 },
    
    #[error("Document is encrypted and requires a password")]
    EncryptedDocument,
    
    #[error("Insufficient permissions: {operation}")]
    PermissionDenied { operation: String },
    
    #[error("Memory allocation failed: {size} bytes")]
    OutOfMemory { size: usize },
    
    #[error("Cache operation failed: {operation}")]
    CacheError { operation: String },
    
    #[error("WebGL context error: {0}")]
    WebGLError(String),
    
    #[error("JavaScript interop error: {0}")]
    JSError(String),
    
    #[error("IO error: {0}")]
    IOError(String),
    
    #[error("Unsupported feature: {feature}")]
    UnsupportedFeature { feature: String },
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Thread synchronization error: {0}")]
    ThreadError(String),
    
    #[error("Invalid document format or corrupted file")]
    InvalidFormat,
    
    #[error("Operation timeout after {seconds} seconds")]
    Timeout { seconds: u64 },
    
    #[error("Unknown error: {0}")]
    Unknown(String),
}

impl PDFError {
    /// Convert to JavaScript error
    pub fn to_js_error(&self) -> JsValue {
        JsValue::from_str(&self.to_string())
    }
    
    /// Get error code for JavaScript
    pub fn error_code(&self) -> &'static str {
        match self {
            PDFError::ParseError(_) => "PARSE_ERROR",
            PDFError::RenderError { .. } => "RENDER_ERROR",
            PDFError::InvalidPageNumber { .. } => "INVALID_PAGE",
            PDFError::EncryptedDocument => "ENCRYPTED_DOCUMENT",
            PDFError::PermissionDenied { .. } => "PERMISSION_DENIED",
            PDFError::OutOfMemory { .. } => "OUT_OF_MEMORY",
            PDFError::CacheError { .. } => "CACHE_ERROR",
            PDFError::WebGLError(_) => "WEBGL_ERROR",
            PDFError::JSError(_) => "JS_ERROR",
            PDFError::IOError(_) => "IO_ERROR",
            PDFError::UnsupportedFeature { .. } => "UNSUPPORTED_FEATURE",
            PDFError::ConfigError(_) => "CONFIG_ERROR",
            PDFError::ThreadError(_) => "THREAD_ERROR",
            PDFError::InvalidFormat => "INVALID_FORMAT",
            PDFError::Timeout { .. } => "TIMEOUT",
            PDFError::Unknown(_) => "UNKNOWN",
        }
    }
    
    /// Check if error is recoverable
    pub fn is_recoverable(&self) -> bool {
        match self {
            PDFError::ParseError(_) => false,
            PDFError::RenderError { .. } => true,
            PDFError::InvalidPageNumber { .. } => false,
            PDFError::EncryptedDocument => false,
            PDFError::PermissionDenied { .. } => false,
            PDFError::OutOfMemory { .. } => true,
            PDFError::CacheError { .. } => true,
            PDFError::WebGLError(_) => true,
            PDFError::JSError(_) => true,
            PDFError::IOError(_) => false,
            PDFError::UnsupportedFeature { .. } => false,
            PDFError::ConfigError(_) => false,
            PDFError::ThreadError(_) => true,
            PDFError::InvalidFormat => false,
            PDFError::Timeout { .. } => true,
            PDFError::Unknown(_) => false,
        }
    }
}

/// Result type alias for PDF operations
pub type PDFResult<T> = Result<T, PDFError>;

/// Convert from pdfium-render errors
#[cfg(feature = "pdfium-parser")]
impl From<pdfium_render::prelude::PdfiumError> for PDFError {
    fn from(err: pdfium_render::prelude::PdfiumError) -> Self {
        PDFError::ParseError(err.to_string())
    }
}

/// Convert from lopdf errors
impl From<lopdf::Error> for PDFError {
    fn from(err: lopdf::Error) -> Self {
        PDFError::ParseError(err.to_string())
    }
}

/// Convert from pdf-rs errors
impl From<pdf::error::PdfError> for PDFError {
    fn from(err: pdf::error::PdfError) -> Self {
        PDFError::ParseError(err.to_string())
    }
}

/// Convert from JavaScript values
impl From<JsValue> for PDFError {
    fn from(js_val: JsValue) -> Self {
        if let Some(s) = js_val.as_string() {
            PDFError::JSError(s)
        } else {
            PDFError::JSError("Unknown JavaScript error".to_string())
        }
    }
}

/// Convert to JavaScript values
impl From<PDFError> for JsValue {
    fn from(err: PDFError) -> Self {
        err.to_js_error()
    }
}

/// Error context for better debugging
#[derive(Debug)]
pub struct ErrorContext {
    pub operation: String,
    pub page: Option<u32>,
    pub timestamp: u64,
    pub additional_info: Option<String>,
}

impl ErrorContext {
    pub fn new(operation: &str) -> Self {
        Self {
            operation: operation.to_string(),
            page: None,
            timestamp: js_sys::Date::now() as u64,
            additional_info: None,
        }
    }
    
    pub fn with_page(mut self, page: u32) -> Self {
        self.page = Some(page);
        self
    }
    
    pub fn with_info(mut self, info: &str) -> Self {
        self.additional_info = Some(info.to_string());
        self
    }
}

/// Macro for creating contextual errors
#[macro_export]
macro_rules! pdf_error {
    ($error_type:expr, $context:expr) => {
        {
            log::error!("PDF Error in {}: {:?}", $context.operation, $error_type);
            $error_type
        }
    };
}

/// Macro for handling recoverable errors
#[macro_export]
macro_rules! handle_recoverable {
    ($result:expr, $fallback:expr) => {
        match $result {
            Ok(val) => val,
            Err(err) if err.is_recoverable() => {
                log::warn!("Recoverable error, using fallback: {}", err);
                $fallback
            },
            Err(err) => return Err(err),
        }
    };
}
