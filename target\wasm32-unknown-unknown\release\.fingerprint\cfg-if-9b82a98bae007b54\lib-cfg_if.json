{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"core\", \"rustc-dep-of-std\"]", "target": 13840298032947503755, "profile": 6418093726722172150, "path": 5142679350089520778, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\cfg-if-9b82a98bae007b54\\dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}