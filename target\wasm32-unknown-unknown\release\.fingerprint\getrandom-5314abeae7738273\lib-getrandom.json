{"rustc": 7868289081541623310, "features": "[\"js\", \"js-sys\", \"wasm-bindgen\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 6418093726722172150, "path": 10552025202119950194, "deps": [[1267401389414215502, "js_sys", false, 9481063980748426554], [7843059260364151289, "cfg_if", false, 10459516428561978112], [17362525766049117937, "wasm_bindgen", false, 1611265920349769740]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\getrandom-5314abeae7738273\\dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}