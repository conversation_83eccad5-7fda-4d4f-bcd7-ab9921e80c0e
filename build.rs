use std::env;
use std::path::PathBuf;

fn main() {
    // Tell cargo to rerun this build script if any of these files change
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=Cargo.toml");
    
    // Set up environment for WebAssembly build
    if env::var("CARGO_CFG_TARGET_ARCH").unwrap_or_default() == "wasm32" {
        // Enable WebAssembly-specific features
        println!("cargo:rustc-cfg=feature=\"wasm\"");

        // Set optimization flags for WebAssembly
        println!("cargo:rustc-env=RUSTFLAGS=-C target-feature=+simd128");
    }
    
    // Check for PDFium library
    if let Ok(pdfium_path) = env::var("PDFIUM_PATH") {
        println!("cargo:rustc-link-search=native={}", pdfium_path);
        println!("cargo:rustc-link-lib=pdfium");
    }
    
    // Set up include paths
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
    println!("cargo:rustc-env=OUT_DIR={}", out_dir.display());
    
    // Generate version information
    let version = env::var("CARGO_PKG_VERSION").unwrap();
    println!("cargo:rustc-env=PDF_ENGINE_VERSION={}", version);
    
    // Check for debug build
    if env::var("PROFILE").unwrap_or_default() == "debug" {
        println!("cargo:rustc-cfg=debug_build");
    }
}
