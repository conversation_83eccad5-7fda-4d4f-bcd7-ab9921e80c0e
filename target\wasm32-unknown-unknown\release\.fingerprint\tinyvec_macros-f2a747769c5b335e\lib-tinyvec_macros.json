{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 15145676655729463769, "profile": 6418093726722172150, "path": 15246182079818456646, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\tinyvec_macros-f2a747769c5b335e\\dep-lib-tinyvec_macros", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}