<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Rendering Engine - Basic Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        input[type="file"] {
            margin-bottom: 10px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .info-panel {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .canvas-container {
            border: 2px solid #dee2e6;
            border-radius: 4px;
            overflow: auto;
            max-height: 800px;
            background: white;
            text-align: center;
            padding: 20px;
        }
        
        #pdf-canvas {
            border: 1px solid #ccc;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 100%;
        }
        
        .loading {
            color: #007bff;
            font-style: italic;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .stat-label {
            font-weight: bold;
            color: #495057;
        }
        
        .stat-value {
            font-size: 18px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 PDF Rendering Engine - Basic Example</h1>
        
        <div class="controls">
            <div class="control-group">
                <label for="pdf-file">Select PDF:</label>
                <input type="file" id="pdf-file" accept=".pdf" />
            </div>
            
            <div class="control-group">
                <label for="page-num">Page:</label>
                <input type="number" id="page-num" value="1" min="1" style="width: 80px;" />
                <button id="prev-page">← Previous</button>
                <button id="next-page">Next →</button>
            </div>
            
            <div class="control-group">
                <label for="scale">Scale:</label>
                <input type="range" id="scale" min="0.5" max="3" step="0.1" value="1" style="width: 200px;" />
                <span id="scale-value">1.0</span>
                <button id="fit-width">Fit Width</button>
                <button id="actual-size">100%</button>
            </div>
            
            <div class="control-group">
                <button id="extract-text">Extract Text</button>
                <button id="search-text">Search</button>
                <input type="text" id="search-query" placeholder="Search text..." style="width: 200px;" />
                <button id="clear-cache">Clear Cache</button>
            </div>
        </div>
        
        <div id="info-panel" class="info-panel">
            <div>Status: <span id="status">Ready</span></div>
            <div>Engine Version: <span id="version">Loading...</span></div>
            <div>Document: <span id="doc-info">No document loaded</span></div>
            <div>Performance: <span id="performance">-</span></div>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-label">Pages</div>
                <div class="stat-value" id="page-count">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Current Page</div>
                <div class="stat-value" id="current-page">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Scale</div>
                <div class="stat-value" id="current-scale">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Memory Usage</div>
                <div class="stat-value" id="memory-usage">-</div>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="pdf-canvas"></canvas>
            <div id="canvas-placeholder" style="color: #6c757d; padding: 40px;">
                Load a PDF file to start rendering
            </div>
        </div>
        
        <div id="text-output" style="display: none; margin-top: 20px;">
            <h3>Extracted Text:</h3>
            <textarea id="extracted-text" style="width: 100%; height: 200px; font-family: monospace;"></textarea>
        </div>
    </div>

    <script type="module">
        // This will be updated to use the actual WASM module once built
        console.log('PDF Rendering Engine Example');
        
        // Mock implementation for demonstration
        class MockPDFEngine {
            constructor() {
                this.pageCount = 0;
                this.currentPage = 1;
                this.scale = 1.0;
            }
            
            async loadDocument(data) {
                // Simulate loading
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                this.pageCount = Math.floor(Math.random() * 20) + 1;
                return {
                    page_count: this.pageCount,
                    title: "Sample PDF Document",
                    author: "PDF Engine",
                    encrypted: false
                };
            }
            
            async renderPage(pageNum, scale, canvasId) {
                const canvas = document.getElementById(canvasId);
                const ctx = canvas.getContext('2d');
                
                // Set canvas size
                canvas.width = 600 * scale;
                canvas.height = 800 * scale;
                
                // Draw a mock page
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = 'black';
                ctx.font = `${16 * scale}px Arial`;
                ctx.fillText(`Page ${pageNum}`, 50 * scale, 50 * scale);
                ctx.fillText(`Scale: ${scale.toFixed(1)}`, 50 * scale, 80 * scale);
                
                // Draw some mock content
                for (let i = 0; i < 10; i++) {
                    ctx.fillText(`This is line ${i + 1} of mock content`, 50 * scale, (120 + i * 30) * scale);
                }
                
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            extractText(pageNum) {
                return `This is extracted text from page ${pageNum}.\n\nThis is a mock implementation that demonstrates the text extraction functionality of the PDF rendering engine.`;
            }
            
            getPageCount() {
                return this.pageCount;
            }
            
            getMemoryUsage() {
                return {
                    total_allocated: Math.floor(Math.random() * 1000000),
                    cache_size: Math.floor(Math.random() * 500000),
                    cached_pages: Math.floor(Math.random() * 10)
                };
            }
        }
        
        // Initialize the application
        let pdfEngine = null;
        let currentDocument = null;
        
        // DOM elements
        const fileInput = document.getElementById('pdf-file');
        const pageNumInput = document.getElementById('page-num');
        const scaleInput = document.getElementById('scale');
        const scaleValue = document.getElementById('scale-value');
        const canvas = document.getElementById('pdf-canvas');
        const canvasPlaceholder = document.getElementById('canvas-placeholder');
        const statusSpan = document.getElementById('status');
        const versionSpan = document.getElementById('version');
        const docInfoSpan = document.getElementById('doc-info');
        const performanceSpan = document.getElementById('performance');
        
        // Initialize
        async function init() {
            try {
                statusSpan.textContent = 'Initializing...';
                
                // For now, use mock engine
                pdfEngine = new MockPDFEngine();
                
                versionSpan.textContent = '0.1.0 (Mock)';
                statusSpan.textContent = 'Ready';
                
                setupEventListeners();
                updateUI();
                
            } catch (error) {
                console.error('Initialization failed:', error);
                statusSpan.textContent = 'Initialization failed';
                showError('Failed to initialize PDF engine: ' + error.message);
            }
        }
        
        function setupEventListeners() {
            fileInput.addEventListener('change', handleFileSelect);
            pageNumInput.addEventListener('change', renderCurrentPage);
            scaleInput.addEventListener('input', handleScaleChange);
            
            document.getElementById('prev-page').addEventListener('click', () => changePage(-1));
            document.getElementById('next-page').addEventListener('click', () => changePage(1));
            document.getElementById('fit-width').addEventListener('click', fitToWidth);
            document.getElementById('actual-size').addEventListener('click', () => setScale(1.0));
            document.getElementById('extract-text').addEventListener('click', extractText);
            document.getElementById('search-text').addEventListener('click', searchText);
            document.getElementById('clear-cache').addEventListener('click', clearCache);
        }
        
        async function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                statusSpan.textContent = 'Loading document...';
                const startTime = performance.now();
                
                const arrayBuffer = await file.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);
                
                const docInfo = await pdfEngine.loadDocument(uint8Array);
                currentDocument = docInfo;
                
                const loadTime = performance.now() - startTime;
                performanceSpan.textContent = `Loaded in ${loadTime.toFixed(1)}ms`;
                
                docInfoSpan.textContent = `${docInfo.page_count} pages`;
                pageNumInput.max = docInfo.page_count;
                pageNumInput.value = 1;
                
                statusSpan.textContent = 'Document loaded';
                canvasPlaceholder.style.display = 'none';
                canvas.style.display = 'block';
                
                await renderCurrentPage();
                updateUI();
                
            } catch (error) {
                console.error('Failed to load document:', error);
                showError('Failed to load PDF: ' + error.message);
                statusSpan.textContent = 'Load failed';
            }
        }
        
        async function renderCurrentPage() {
            if (!currentDocument) return;
            
            const pageNum = parseInt(pageNumInput.value);
            const scale = parseFloat(scaleInput.value);
            
            try {
                statusSpan.textContent = 'Rendering...';
                const startTime = performance.now();
                
                await pdfEngine.renderPage(pageNum, scale, 'pdf-canvas');
                
                const renderTime = performance.now() - startTime;
                performanceSpan.textContent = `Rendered in ${renderTime.toFixed(1)}ms`;
                statusSpan.textContent = 'Ready';
                
                updateUI();
                
            } catch (error) {
                console.error('Render failed:', error);
                showError('Failed to render page: ' + error.message);
                statusSpan.textContent = 'Render failed';
            }
        }
        
        function handleScaleChange() {
            const scale = parseFloat(scaleInput.value);
            scaleValue.textContent = scale.toFixed(1);
            renderCurrentPage();
        }
        
        function changePage(delta) {
            if (!currentDocument) return;
            
            const newPage = parseInt(pageNumInput.value) + delta;
            if (newPage >= 1 && newPage <= currentDocument.page_count) {
                pageNumInput.value = newPage;
                renderCurrentPage();
            }
        }
        
        function setScale(scale) {
            scaleInput.value = scale;
            scaleValue.textContent = scale.toFixed(1);
            renderCurrentPage();
        }
        
        function fitToWidth() {
            // Mock implementation
            const containerWidth = document.querySelector('.canvas-container').clientWidth - 40;
            const scale = containerWidth / 600; // Assuming 600px base width
            setScale(Math.min(scale, 3.0));
        }
        
        function extractText() {
            if (!currentDocument) return;
            
            const pageNum = parseInt(pageNumInput.value);
            const text = pdfEngine.extractText(pageNum);
            
            document.getElementById('extracted-text').value = text;
            document.getElementById('text-output').style.display = 'block';
        }
        
        function searchText() {
            const query = document.getElementById('search-query').value;
            if (!query || !currentDocument) return;
            
            showSuccess(`Searching for "${query}" - Mock implementation`);
        }
        
        function clearCache() {
            showSuccess('Cache cleared');
            updateUI();
        }
        
        function updateUI() {
            if (currentDocument) {
                document.getElementById('page-count').textContent = currentDocument.page_count;
                document.getElementById('current-page').textContent = pageNumInput.value;
                document.getElementById('current-scale').textContent = scaleInput.value;
                
                const memUsage = pdfEngine.getMemoryUsage();
                document.getElementById('memory-usage').textContent = 
                    `${Math.round(memUsage.total_allocated / 1024)}KB`;
                
                // Update button states
                document.getElementById('prev-page').disabled = pageNumInput.value <= 1;
                document.getElementById('next-page').disabled = pageNumInput.value >= currentDocument.page_count;
            }
        }
        
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.controls'));
            setTimeout(() => errorDiv.remove(), 5000);
        }
        
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.controls'));
            setTimeout(() => successDiv.remove(), 3000);
        }
        
        // Start the application
        init();
    </script>
</body>
</html>
