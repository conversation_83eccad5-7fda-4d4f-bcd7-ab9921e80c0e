use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;

/// Document information returned after loading
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[wasm_bindgen]
pub struct DocumentInfo {
    page_count: u32,
    title: Option<String>,
    author: Option<String>,
    subject: Option<String>,
    creator: Option<String>,
    producer: Option<String>,
    creation_date: Option<String>,
    modification_date: Option<String>,
    encrypted: bool,
    permissions: DocumentPermissions,
}

#[wasm_bindgen]
impl DocumentInfo {
    #[wasm_bindgen(getter)]
    pub fn page_count(&self) -> u32 { self.page_count }

    #[wasm_bindgen(getter)]
    pub fn title(&self) -> Option<String> { self.title.clone() }

    #[wasm_bindgen(getter)]
    pub fn author(&self) -> Option<String> { self.author.clone() }

    #[wasm_bindgen(getter)]
    pub fn subject(&self) -> Option<String> { self.subject.clone() }

    #[wasm_bindgen(getter)]
    pub fn creator(&self) -> Option<String> { self.creator.clone() }

    #[wasm_bindgen(getter)]
    pub fn producer(&self) -> Option<String> { self.producer.clone() }

    #[wasm_bindgen(getter)]
    pub fn creation_date(&self) -> Option<String> { self.creation_date.clone() }

    #[wasm_bindgen(getter)]
    pub fn modification_date(&self) -> Option<String> { self.modification_date.clone() }

    #[wasm_bindgen(getter)]
    pub fn encrypted(&self) -> bool { self.encrypted }

    #[wasm_bindgen(getter)]
    pub fn permissions(&self) -> DocumentPermissions { self.permissions.clone() }
}

impl DocumentInfo {
    pub fn new(
        page_count: u32,
        title: Option<String>,
        author: Option<String>,
        subject: Option<String>,
        creator: Option<String>,
        producer: Option<String>,
        creation_date: Option<String>,
        modification_date: Option<String>,
        encrypted: bool,
        permissions: DocumentPermissions,
    ) -> Self {
        Self {
            page_count,
            title,
            author,
            subject,
            creator,
            producer,
            creation_date,
            modification_date,
            encrypted,
            permissions,
        }
    }
}

/// Document permissions
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
#[wasm_bindgen]
pub struct DocumentPermissions {
    pub can_print: bool,
    pub can_modify: bool,
    pub can_copy: bool,
    pub can_add_notes: bool,
    pub can_fill_forms: bool,
    pub can_extract_for_accessibility: bool,
    pub can_assemble: bool,
    pub can_print_high_quality: bool,
}

/// Page size information
#[derive(Debug, Clone, Serialize, Deserialize)]
#[wasm_bindgen]
pub struct PageSize {
    pub width: f32,
    pub height: f32,
    pub rotation: i32,
}

/// Text search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub page_num: u32,
    pub text: String,
    pub bounds: Vec<TextBounds>,
}

/// Text bounds for highlighting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextBounds {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

/// Memory usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryUsage {
    pub total_allocated: usize,
    pub cache_size: usize,
    pub active_pages: usize,
    pub cached_pages: usize,
}

/// Rendering options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RenderOptions {
    pub scale: f32,
    pub rotation: i32,
    pub background_color: Option<String>,
    pub annotations: bool,
    pub forms: bool,
    pub grayscale: bool,
}

impl Default for RenderOptions {
    fn default() -> Self {
        Self {
            scale: 1.0,
            rotation: 0,
            background_color: Some("#FFFFFF".to_string()),
            annotations: true,
            forms: true,
            grayscale: false,
        }
    }
}

/// Page rendering result
#[derive(Debug, Clone)]
pub struct RenderedPage {
    pub page_num: u32,
    pub width: u32,
    pub height: u32,
    pub scale: f32,
    pub image_data: Vec<u8>,
    pub timestamp: u64,
}

impl RenderedPage {
    pub fn data_ptr(&self) -> *const u8 {
        self.image_data.as_ptr()
    }
    
    pub fn data_len(&self) -> usize {
        self.image_data.len()
    }
}

/// Cache entry for rendered pages
#[derive(Debug, Clone)]
pub struct CacheEntry {
    pub page: RenderedPage,
    pub access_count: u32,
    pub last_accessed: u64,
}

/// PDF parsing backend type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ParserBackend {
    PDFium,
    Lopdf,
    PdfRs,
}

/// Rendering backend type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RenderBackend {
    PDFium,
    Canvas2D,
    WebGL,
}

/// Engine configuration
#[derive(Debug, Clone)]
pub struct EngineConfig {
    pub parser_backend: ParserBackend,
    pub render_backend: RenderBackend,
    pub cache_size_mb: usize,
    pub max_cached_pages: usize,
    pub enable_threading: bool,
    pub enable_webgl: bool,
    pub memory_limit_mb: usize,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            parser_backend: ParserBackend::PDFium,
            render_backend: RenderBackend::WebGL,
            cache_size_mb: 100,
            max_cached_pages: 50,
            enable_threading: true,
            enable_webgl: true,
            memory_limit_mb: 500,
        }
    }
}

/// Point structure for coordinates
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Point {
    pub x: f32,
    pub y: f32,
}

/// Rectangle structure for bounds
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Rect {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

impl Rect {
    pub fn new(x: f32, y: f32, width: f32, height: f32) -> Self {
        Self { x, y, width, height }
    }
    
    pub fn contains(&self, point: Point) -> bool {
        point.x >= self.x && 
        point.x <= self.x + self.width &&
        point.y >= self.y && 
        point.y <= self.y + self.height
    }
    
    pub fn intersects(&self, other: &Rect) -> bool {
        self.x < other.x + other.width &&
        self.x + self.width > other.x &&
        self.y < other.y + other.height &&
        self.y + self.height > other.y
    }
}
