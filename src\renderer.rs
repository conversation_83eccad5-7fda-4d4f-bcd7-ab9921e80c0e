use crate::types::*;
use crate::error::{PDFError, PDFResult};

use wasm_bindgen::prelude::*;
use wasm_bindgen::JsCast;
use web_sys::{
    console, window, HtmlCanvasElement, CanvasRenderingContext2d,
    WebGlRenderingContext, ImageData
};
use js_sys::Uint8ClampedArray;

/// Renderer for PDF pages with multiple backend support
pub struct Renderer {
    backend: RenderBackend,
    webgl_enabled: bool,
    canvas_cache: std::collections::HashMap<String, HtmlCanvasElement>,
}

impl Renderer {
    /// Create a new renderer
    pub fn new(backend: RenderBackend, webgl_enabled: bool) -> PDFResult<Self> {
        console::log_1(&format!("Initializing renderer: {:?}, WebGL: {}", backend, webgl_enabled).into());
        
        // Validate WebGL support if requested
        if webgl_enabled && backend == RenderBackend::WebGL {
            Self::check_webgl_support()?;
        }
        
        Ok(Renderer {
            backend,
            webgl_enabled,
            canvas_cache: std::collections::HashMap::new(),
        })
    }
    
    /// Check if WebGL is supported
    fn check_webgl_support() -> PDFResult<()> {
        let window = window().ok_or_else(|| PDFError::WebGLError("No window object".to_string()))?;
        let document = window.document().ok_or_else(|| PDFError::WebGLError("No document".to_string()))?;
        
        // Create a temporary canvas to test WebGL
        let canvas = document.create_element("canvas")
            .map_err(|_| PDFError::WebGLError("Failed to create test canvas".to_string()))?
            .dyn_into::<HtmlCanvasElement>()
            .map_err(|_| PDFError::WebGLError("Failed to cast to canvas".to_string()))?;
        
        // Try to get WebGL context
        let webgl_context = canvas.get_context("webgl")
            .map_err(|_| PDFError::WebGLError("Failed to get WebGL context".to_string()))?;
        
        if webgl_context.is_none() {
            return Err(PDFError::WebGLError("WebGL not supported".to_string()));
        }
        
        console::log_1(&"WebGL support confirmed".into());
        Ok(())
    }
    
    /// Render a page to a canvas element
    pub async fn render_to_canvas(&self, page_data: &RenderedPage, canvas_id: &str) -> PDFResult<()> {
        match self.backend {
            RenderBackend::Canvas2D => self.render_with_canvas2d(page_data, canvas_id).await,
            RenderBackend::WebGL => self.render_with_webgl(page_data, canvas_id).await,
            RenderBackend::PDFium => self.render_with_pdfium(page_data, canvas_id).await,
        }
    }
    
    /// Render using Canvas 2D API
    async fn render_with_canvas2d(&self, page_data: &RenderedPage, canvas_id: &str) -> PDFResult<()> {
        let canvas = self.get_canvas(canvas_id)?;
        
        // Set canvas size
        canvas.set_width(page_data.width);
        canvas.set_height(page_data.height);
        
        // Get 2D context
        let context = canvas.get_context("2d")
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to get 2D context".to_string() 
            })?
            .ok_or_else(|| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "2D context is null".to_string() 
            })?
            .dyn_into::<CanvasRenderingContext2d>()
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to cast to 2D context".to_string() 
            })?;
        
        // Create ImageData from page data
        let image_data = self.create_image_data(&page_data.image_data, page_data.width, page_data.height)?;
        
        // Draw to canvas
        context.put_image_data(&image_data, 0.0, 0.0)
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to put image data".to_string() 
            })?;
        
        console::log_1(&format!("Page {} rendered with Canvas2D", page_data.page_num).into());
        Ok(())
    }
    
    /// Render using WebGL
    async fn render_with_webgl(&self, page_data: &RenderedPage, canvas_id: &str) -> PDFResult<()> {
        if !self.webgl_enabled {
            // Fallback to Canvas2D
            return self.render_with_canvas2d(page_data, canvas_id).await;
        }
        
        let canvas = self.get_canvas(canvas_id)?;
        
        // Set canvas size
        canvas.set_width(page_data.width);
        canvas.set_height(page_data.height);
        
        // Get WebGL context
        let gl = canvas.get_context("webgl")
            .map_err(|_| PDFError::WebGLError("Failed to get WebGL context".to_string()))?
            .ok_or_else(|| PDFError::WebGLError("WebGL context is null".to_string()))?
            .dyn_into::<WebGlRenderingContext>()
            .map_err(|_| PDFError::WebGLError("Failed to cast to WebGL context".to_string()))?;
        
        // Set viewport
        gl.viewport(0, 0, page_data.width as i32, page_data.height as i32);
        
        // Clear canvas
        gl.clear_color(1.0, 1.0, 1.0, 1.0);
        gl.clear(WebGlRenderingContext::COLOR_BUFFER_BIT);
        
        // For now, fallback to Canvas2D for actual rendering
        // TODO: Implement full WebGL texture-based rendering
        console::log_1(&"WebGL rendering not fully implemented, falling back to Canvas2D".into());
        self.render_with_canvas2d(page_data, canvas_id).await
    }
    
    /// Render using PDFium (direct rendering)
    async fn render_with_pdfium(&self, page_data: &RenderedPage, canvas_id: &str) -> PDFResult<()> {
        // This would be used when PDFium can render directly to canvas
        // For now, use the image data approach
        self.render_with_canvas2d(page_data, canvas_id).await
    }
    
    /// Get or create a canvas element
    fn get_canvas(&self, canvas_id: &str) -> PDFResult<HtmlCanvasElement> {
        let window = window().ok_or_else(|| PDFError::JSError("No window object".to_string()))?;
        let document = window.document().ok_or_else(|| PDFError::JSError("No document".to_string()))?;
        
        // Try to get existing canvas
        if let Some(element) = document.get_element_by_id(canvas_id) {
            element.dyn_into::<HtmlCanvasElement>()
                .map_err(|_| PDFError::JSError(format!("Element '{}' is not a canvas", canvas_id)))
        } else {
            Err(PDFError::JSError(format!("Canvas element '{}' not found", canvas_id)))
        }
    }
    
    /// Create ImageData from raw pixel data
    fn create_image_data(&self, data: &[u8], width: u32, height: u32) -> PDFResult<ImageData> {
        // Convert RGBA data to Uint8ClampedArray
        let clamped_array = Uint8ClampedArray::new_with_length(data.len() as u32);
        clamped_array.copy_from(data);
        
        // Create ImageData
        ImageData::new_with_u8_clamped_array_and_sh(wasm_bindgen::Clamped(&clamped_array.to_vec()), width, height)
            .map_err(|_| PDFError::RenderError { 
                page: 0, 
                reason: "Failed to create ImageData".to_string() 
            })
    }
    
    /// Render page to an offscreen canvas and return as blob
    pub async fn render_to_blob(&self, page_data: &RenderedPage, format: &str) -> PDFResult<web_sys::Blob> {
        let window = window().ok_or_else(|| PDFError::JSError("No window object".to_string()))?;
        let document = window.document().ok_or_else(|| PDFError::JSError("No document".to_string()))?;
        
        // Create offscreen canvas
        let canvas = document.create_element("canvas")
            .map_err(|_| PDFError::JSError("Failed to create offscreen canvas".to_string()))?
            .dyn_into::<HtmlCanvasElement>()
            .map_err(|_| PDFError::JSError("Failed to cast to canvas".to_string()))?;
        
        canvas.set_width(page_data.width);
        canvas.set_height(page_data.height);
        
        // Render to offscreen canvas
        let context = canvas.get_context("2d")
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to get 2D context".to_string() 
            })?
            .ok_or_else(|| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "2D context is null".to_string() 
            })?
            .dyn_into::<CanvasRenderingContext2d>()
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to cast to 2D context".to_string() 
            })?;
        
        let image_data = self.create_image_data(&page_data.image_data, page_data.width, page_data.height)?;
        context.put_image_data(&image_data, 0.0, 0.0)
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to put image data".to_string() 
            })?;
        
        // Convert to blob - simplified for now
        // Note: to_blob_with_type requires a callback, this is a simplified implementation
        let blob_value = js_sys::Object::new();
        
        blob_value.dyn_into::<web_sys::Blob>()
            .map_err(|_| PDFError::RenderError { 
                page: page_data.page_num, 
                reason: "Failed to cast to Blob".to_string() 
            })
    }
    
    /// Get renderer capabilities
    pub fn get_capabilities(&self) -> RendererCapabilities {
        RendererCapabilities {
            backend: self.backend,
            webgl_supported: self.webgl_enabled,
            max_texture_size: if self.webgl_enabled { Some(4096) } else { None },
            supported_formats: vec!["image/png".to_string(), "image/jpeg".to_string()],
        }
    }
    
    /// Clear canvas
    pub fn clear_canvas(&self, canvas_id: &str) -> PDFResult<()> {
        let canvas = self.get_canvas(canvas_id)?;
        let context = canvas.get_context("2d")
            .map_err(|_| PDFError::JSError("Failed to get 2D context".to_string()))?
            .ok_or_else(|| PDFError::JSError("2D context is null".to_string()))?
            .dyn_into::<CanvasRenderingContext2d>()
            .map_err(|_| PDFError::JSError("Failed to cast to 2D context".to_string()))?;
        
        context.clear_rect(0.0, 0.0, canvas.width() as f64, canvas.height() as f64);
        Ok(())
    }
}

/// Renderer capabilities
#[derive(Debug, Clone)]
pub struct RendererCapabilities {
    pub backend: RenderBackend,
    pub webgl_supported: bool,
    pub max_texture_size: Option<u32>,
    pub supported_formats: Vec<String>,
}
