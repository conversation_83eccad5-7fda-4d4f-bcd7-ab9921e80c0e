{"rustc": 7868289081541623310, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 17249443592550183152, "path": 3933113628662909918, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\allocator-api2-35f82e25c68c33cd\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}