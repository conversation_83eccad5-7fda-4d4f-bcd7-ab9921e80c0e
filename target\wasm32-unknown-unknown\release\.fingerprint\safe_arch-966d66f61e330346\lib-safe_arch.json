{"rustc": 7868289081541623310, "features": "[\"bytemuck\", \"default\"]", "declared_features": "[\"bytemuck\", \"default\"]", "target": 9287881243760046938, "profile": 6418093726722172150, "path": 7649923654402666989, "deps": [[6643739152182419278, "bytemuck", false, 2866764535363635215]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\safe_arch-966d66f61e330346\\dep-lib-safe_arch", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}