{"rustc": 7868289081541623310, "features": "[\"default\", \"rust_1_39\", \"rust_1_46\", \"std\"]", "declared_features": "[\"backtrace\", \"backtraces\", \"backtraces-impl-backtrace-crate\", \"backtraces-impl-std\", \"default\", \"futures\", \"futures-core-crate\", \"futures-crate\", \"guide\", \"internal-dev-dependencies\", \"pin-project\", \"rust_1_39\", \"rust_1_46\", \"rust_1_61\", \"std\", \"unstable-backtraces-impl-std\", \"unstable-core-error\", \"unstable-provider-api\", \"unstable-try-trait\"]", "target": 12994127008901877218, "profile": 6418093726722172150, "path": 9218721510765835576, "deps": [[389463616194444828, "snafu_derive", false, 12384724332372635082], [18000218614148971598, "doc_comment", false, 15213444186290467301]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\snafu-f6f2cfb2f57bc3e9\\dep-lib-snafu", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}