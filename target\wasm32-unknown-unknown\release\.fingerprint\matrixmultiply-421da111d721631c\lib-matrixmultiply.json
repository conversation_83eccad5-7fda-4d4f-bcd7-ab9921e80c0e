{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"cgemm\", \"constconf\", \"default\", \"num_cpus\", \"once_cell\", \"std\", \"thread-tree\", \"threading\"]", "target": 7055067433712553826, "profile": 6418093726722172150, "path": 13430345771230512115, "deps": [[15709748443193639506, "rawpointer", false, 3210637963362610936], [15826188163127377936, "build_script_build", false, 4245812643047690173]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\matrixmultiply-421da111d721631c\\dep-lib-matrixmultiply", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}