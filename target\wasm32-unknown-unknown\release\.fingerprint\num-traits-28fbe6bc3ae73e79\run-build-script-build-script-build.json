{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5157631553186200874, "build_script_build", false, 2316128223844604571]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\num-traits-28fbe6bc3ae73e79\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}