{"rustc": 7868289081541623310, "features": "[\"default\", \"derive\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 11327258112168116673, "profile": 6418093726722172150, "path": 12909581454039846745, "deps": [[1511010206896078948, "serde_derive", false, 13005658624759083687], [2814771377364143997, "serde_core", false, 9658443668891853290], [14851956875005785803, "build_script_build", false, 137937121581569839]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\serde-a3a0d8c1936d97e7\\dep-lib-serde", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}