{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1852463361802237065, "build_script_build", false, 2832182373986698889]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\anyhow-590cf6a9f5056e5c\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}