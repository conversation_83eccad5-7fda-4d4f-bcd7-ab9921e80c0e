{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"ignore-missing-operators\"]", "target": 8396726190550998917, "profile": 6418093726722172150, "path": 7847674046057809400, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\postscript-fc5fa722a0abc0fe\\dep-lib-postscript", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}