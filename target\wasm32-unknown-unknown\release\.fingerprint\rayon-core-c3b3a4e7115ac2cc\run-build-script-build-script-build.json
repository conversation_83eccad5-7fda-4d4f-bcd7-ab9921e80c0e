{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3746573929696391749, "build_script_build", false, 6539535480467112330]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\rayon-core-c3b3a4e7115ac2cc\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}