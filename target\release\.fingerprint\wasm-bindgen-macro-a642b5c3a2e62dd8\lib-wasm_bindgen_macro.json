{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"strict-macro\"]", "target": 6875603382767429092, "profile": 16199798871480566777, "path": 13183774904344741006, "deps": [[4190627925692779123, "wasm_bindgen_macro_support", false, 16228780345767055011], [17990358020177143287, "quote", false, 16806568807362843329]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wasm-bindgen-macro-a642b5c3a2e62dd8\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}