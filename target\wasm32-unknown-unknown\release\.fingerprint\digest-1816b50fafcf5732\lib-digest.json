{"rustc": 7868289081541623310, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 6418093726722172150, "path": 177554564215007717, "deps": [[2352660017780662552, "crypto_common", false, 497825291861482611], [10626340395483396037, "block_buffer", false, 2410862976150107992]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\digest-1816b50fafcf5732\\dep-lib-digest", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}