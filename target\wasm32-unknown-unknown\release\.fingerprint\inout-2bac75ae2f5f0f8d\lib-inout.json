{"rustc": 7868289081541623310, "features": "[\"block-padding\"]", "declared_features": "[\"block-padding\", \"std\"]", "target": 16139718221464202370, "profile": 6418093726722172150, "path": 12334406014880107745, "deps": [[10520923840501062997, "generic_array", false, 13496954508381988677], [13624526718496097675, "block_padding", false, 11414680968255111739]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\inout-2bac75ae2f5f0f8d\\dep-lib-inout", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}