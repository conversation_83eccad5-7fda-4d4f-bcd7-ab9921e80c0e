#!/bin/bash

# Install Rust and required tools for PDF Rendering Engine

echo "🦀 Installing Rust and required tools for PDF Rendering Engine..."

# Check if we're on Windows (Git Bash/WSL)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "Detected Windows environment"
    
    # Download and run rustup installer for Windows
    echo "📥 Downloading Rust installer..."
    curl --proto '=https' --tlsv1.2 -sSf https://win.rustup.rs/x86_64 -o rustup-init.exe
    
    echo "🚀 Running Rust installer..."
    ./rustup-init.exe -y --default-toolchain stable
    
    # Add to PATH for current session
    export PATH="$HOME/.cargo/bin:$PATH"
    
    # Clean up installer
    rm rustup-init.exe
    
else
    # Unix-like systems (Linux, macOS, WSL)
    echo "Detected Unix-like environment"
    
    # Download and run rustup installer
    echo "📥 Downloading and installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain stable
    
    # Source the cargo environment
    source "$HOME/.cargo/env"
fi

# Verify Rust installation
echo "🔍 Verifying Rust installation..."
if command -v rustc &> /dev/null; then
    echo "✅ Rust installed successfully!"
    rustc --version
    cargo --version
else
    echo "❌ Rust installation failed. Please install manually from https://rustup.rs/"
    exit 1
fi

# Install required targets and tools
echo "📦 Installing WebAssembly target..."
rustup target add wasm32-unknown-unknown

echo "📦 Installing wasm-pack..."
if command -v wasm-pack &> /dev/null; then
    echo "wasm-pack already installed"
else
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        # Windows installation
        curl -L https://github.com/rustwasm/wasm-pack/releases/latest/download/wasm-pack-init.exe -o wasm-pack-init.exe
        ./wasm-pack-init.exe
        rm wasm-pack-init.exe
    else
        # Unix-like installation
        curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
    fi
fi

# Verify wasm-pack installation
if command -v wasm-pack &> /dev/null; then
    echo "✅ wasm-pack installed successfully!"
    wasm-pack --version
else
    echo "⚠️  wasm-pack installation may have failed. You can install it manually."
fi

# Install additional tools (optional but recommended)
echo "📦 Installing additional tools..."

# Install wasm-opt for optimization (part of binaryen)
if command -v wasm-opt &> /dev/null; then
    echo "wasm-opt already installed"
else
    echo "⚠️  wasm-opt not found. Install binaryen for better optimization:"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "  Ubuntu/Debian: sudo apt install binaryen"
        echo "  Arch: sudo pacman -S binaryen"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "  macOS: brew install binaryen"
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        echo "  Windows: Download from https://github.com/WebAssembly/binaryen/releases"
    fi
fi

# Install Node.js if not present (for examples and testing)
if command -v node &> /dev/null; then
    echo "✅ Node.js already installed: $(node --version)"
else
    echo "⚠️  Node.js not found. Install from https://nodejs.org/ for examples and testing."
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Restart your terminal or run: source ~/.cargo/env"
echo "2. Build the project: ./wasm-pack-build.sh"
echo "3. Test with examples: python -m http.server 8000"
echo ""
echo "Installed tools:"
echo "  - Rust: $(rustc --version 2>/dev/null || echo 'Not in PATH yet')"
echo "  - Cargo: $(cargo --version 2>/dev/null || echo 'Not in PATH yet')"
echo "  - wasm-pack: $(wasm-pack --version 2>/dev/null || echo 'Not installed')"
echo "  - wasm32 target: $(rustup target list --installed | grep wasm32 || echo 'Not installed')"
echo ""
echo "If you encounter issues, please check:"
echo "  - PATH includes ~/.cargo/bin"
echo "  - Restart terminal after installation"
echo "  - Manual installation from https://rustup.rs/"
