{"rustc": 7868289081541623310, "features": "[\"simd-adler32\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"simd-adler32\", \"std\", \"zlib\"]", "target": 12020662131698132232, "profile": 6418093726722172150, "path": 1867639236521283962, "deps": [[4018467389006652250, "simd_adler32", false, 15883599475675365607]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\zune-inflate-cf0f3e0e7d9ca148\\dep-lib-zune_inflate", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}