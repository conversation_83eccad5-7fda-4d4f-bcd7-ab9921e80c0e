{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4468123440088164316, "build_script_build", false, 6033233785510562905]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\crossbeam-utils-7ae563a368b2640c\\output", "paths": ["no_atomic.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}