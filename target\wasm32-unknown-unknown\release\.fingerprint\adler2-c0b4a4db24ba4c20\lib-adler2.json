{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"core\", \"default\", \"rustc-dep-of-std\", \"std\"]", "target": 6569825234462323107, "profile": 6418093726722172150, "path": 472059426708796855, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\adler2-c0b4a4db24ba4c20\\dep-lib-adler2", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}