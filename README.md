# PDF Rendering Engine

A high-performance PDF rendering engine built with Rust and WebAssembly, featuring hybrid parser support and intelligent caching.

## 🚀 Features

### Core Capabilities
- **High-Performance Rendering**: WebAssembly-powered PDF rendering with GPU acceleration
- **Hybrid Parser Support**: PDFium (primary) with pure Rust fallbacks (lopdf, pdf-rs)
- **Multiple Rendering Backends**: Canvas2D, WebGL, and direct PDFium rendering
- **Intelligent Caching**: LRU cache with memory management and optimization
- **Text Processing**: Text extraction, search, and selection
- **Cross-Platform**: Web browsers, Node.js, and native applications

### Performance Optimizations
- **Lazy Loading**: Pages loaded on-demand
- **Progressive Rendering**: Low-res preview with enhancement
- **Virtual Scrolling**: Only render visible pages
- **Memory Pools**: Efficient memory allocation and management
- **SharedArrayBuffer**: Multi-threaded processing support
- **SIMD Operations**: Vectorized operations where supported

## 🏗️ Architecture

```
┌─────────────────────────────────────────┐
│           WebAssembly Interface         │
├─────────────────────────────────────────┤
│  PDF Engine (Rust)                     │
│  ├── Parser (PDFium + Rust fallbacks)  │
│  ├── Renderer (Canvas2D/WebGL)         │
│  ├── Cache (LRU + Memory Pool)         │
│  └── Utils (Performance, Validation)   │
├─────────────────────────────────────────┤
│  Browser APIs (Canvas, WebGL, Workers) │
└─────────────────────────────────────────┘
```

## 📦 Installation

### Prerequisites
- Rust 1.70+ with `wasm32-unknown-unknown` target
- wasm-pack for WebAssembly builds
- Node.js 14+ (for examples and testing)

### Building from Source

```bash
# Clone the repository
git clone https://github.com/your-org/pdf-render-engine
cd pdf-render-engine

# Install Rust target
rustup target add wasm32-unknown-unknown

# Install wasm-pack
curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh

# Build for all targets
./wasm-pack-build.sh
```

### Using Pre-built Packages

```bash
npm install pdf-render-engine
```

## 🔧 Usage

### Web (ES Modules)

```javascript
import init, { WasmPDFEngine } from 'pdf-render-engine/web/pdf_render_engine.js';

async function loadPDF() {
    // Initialize the WebAssembly module
    await init();
    
    // Create engine instance
    const engine = new WasmPDFEngine();
    
    // Load PDF document
    const pdfData = new Uint8Array(/* your PDF data */);
    const docInfo = await engine.load_document(pdfData);
    
    console.log(`Loaded PDF with ${docInfo.page_count} pages`);
    
    // Render first page
    await engine.render_page(1, 1.0, 'canvas-id');
    
    // Extract text
    const text = engine.extract_text(1);
    console.log('Page text:', text);
    
    // Search for text
    const results = engine.search_text('search query');
    console.log('Search results:', results);
}
```

### Bundler (Webpack, Vite, etc.)

```javascript
import init, { WasmPDFEngine } from 'pdf-render-engine';

// Same usage as above
```

### Node.js

```javascript
const { WasmPDFEngine } = require('pdf-render-engine/nodejs/pdf_render_engine.js');

// Node.js usage (text extraction, no canvas rendering)
const engine = new WasmPDFEngine();
// ... rest of the API
```

## 🎯 API Reference

### WasmPDFEngine

#### Constructor
```javascript
const engine = new WasmPDFEngine();
```

#### Methods

##### `load_document(data: Uint8Array): Promise<DocumentInfo>`
Load a PDF document from binary data.

```javascript
const docInfo = await engine.load_document(pdfData);
// Returns: { page_count, title, author, encrypted, permissions, ... }
```

##### `render_page(pageNum: number, scale: number, canvasId: string): Promise<void>`
Render a page to a canvas element.

```javascript
await engine.render_page(1, 1.5, 'my-canvas');
```

##### `get_page_count(): number`
Get the total number of pages.

##### `get_page_size(pageNum: number): PageSize`
Get page dimensions and rotation.

##### `extract_text(pageNum: number): string`
Extract text content from a page.

##### `search_text(query: string): SearchResult[]`
Search for text across the document.

##### `clear_cache(): void`
Clear the rendering cache.

##### `get_memory_usage(): MemoryUsage`
Get memory usage statistics.

## 🔧 Configuration

### Engine Configuration

```javascript
// The engine uses intelligent defaults, but you can customize:
// - Parser backend (PDFium, lopdf, pdf-rs)
// - Render backend (Canvas2D, WebGL)
// - Cache size and limits
// - Memory management settings
```

### Performance Tuning

```javascript
// Cache optimization
engine.clear_cache(); // Clear when memory is low

// Memory monitoring
const usage = engine.get_memory_usage();
console.log(`Cache: ${usage.cache_size}KB, Pages: ${usage.cached_pages}`);
```

## 🎨 Examples

### Basic PDF Viewer
See `examples/basic.html` for a complete PDF viewer implementation.

### Advanced Features
```javascript
// Progressive loading with caching
for (let page = 1; page <= docInfo.page_count; page++) {
    // Pre-render pages in background
    setTimeout(() => engine.render_page(page, 1.0, `canvas-${page}`), page * 100);
}

// Text search with highlighting
const results = engine.search_text('important text');
results.forEach(result => {
    console.log(`Found on page ${result.page_num}: ${result.text}`);
});

// Memory management
setInterval(() => {
    const usage = engine.get_memory_usage();
    if (usage.cache_size > 50 * 1024 * 1024) { // 50MB limit
        engine.clear_cache();
    }
}, 30000);
```

## 🔍 Browser Support

| Browser | Version | WebAssembly | WebGL | SharedArrayBuffer |
|---------|---------|-------------|-------|-------------------|
| Chrome  | 57+     | ✅          | ✅     | ✅                |
| Firefox | 52+     | ✅          | ✅     | ✅                |
| Safari  | 11+     | ✅          | ✅     | ⚠️ (requires headers) |
| Edge    | 16+     | ✅          | ✅     | ✅                |

## 📊 Performance

### Benchmarks
- **Initial Load**: < 2 seconds for typical documents
- **Page Rendering**: < 500ms for 100-page documents
- **Text Search**: < 100ms for 1000-page documents
- **Memory Usage**: < 500MB for large documents

### Optimization Features
- Intelligent caching with LRU eviction
- Progressive rendering for immediate feedback
- Memory pools for efficient allocation
- Multi-threaded processing with Web Workers

## 🛠️ Development

### Building
```bash
# Development build
cargo build

# Release build with optimizations
./wasm-pack-build.sh

# Run tests
cargo test
```

### Testing
```bash
# Unit tests
cargo test

# Integration tests with browser
wasm-pack test --headless --firefox

# Manual testing
python -m http.server 8000
# Open http://localhost:8000/examples/basic.html
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- [PDFium](https://pdfium.googlesource.com/pdfium/) - Google's PDF rendering library
- [lopdf](https://github.com/J-F-Liu/lopdf) - Pure Rust PDF library
- [wasm-bindgen](https://github.com/rustwasm/wasm-bindgen) - WebAssembly bindings
- [web-sys](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/web-sys) - Web API bindings
