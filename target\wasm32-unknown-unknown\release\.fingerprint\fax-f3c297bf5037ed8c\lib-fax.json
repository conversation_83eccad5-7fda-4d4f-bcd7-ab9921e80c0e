{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"debug\"]", "target": 1089503997142669903, "profile": 6418093726722172150, "path": 10352429709062503162, "deps": [[16896555084957406727, "fax_derive", false, 6294792150049942243]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\fax-f3c297bf5037ed8c\\dep-lib-fax", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}