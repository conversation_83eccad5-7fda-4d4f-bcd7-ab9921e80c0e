{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 16333205938936660957, "path": 13968130968443794016, "deps": [[7826122624549889939, "build_script_build", false, 10018589830576237869], [10637008577242657367, "unicode_ident", false, 10573581634414939127]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\wasm-bindgen-shared-9d097d705da801f7\\dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}