{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 6418093726722172150, "path": 13733573607668241669, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\hashbrown-0421f13c0eeb9cfc\\dep-lib-hashbrown", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}