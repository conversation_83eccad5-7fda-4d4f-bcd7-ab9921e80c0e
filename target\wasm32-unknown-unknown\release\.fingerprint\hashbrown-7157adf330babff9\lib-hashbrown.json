{"rustc": 7868289081541623310, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 6418093726722172150, "path": 11499000141761425681, "deps": [[5230392855116717286, "equivalent", false, 9368928829673299149], [9150530836556604396, "allocator_api2", false, 917285654116523656], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 12297996804768444050]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\hashbrown-7157adf330babff9\\dep-lib-hashbrown", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}