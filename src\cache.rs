use crate::types::*;
use crate::error::{PDFError, PDFResult};

use lru::LruCache;
use std::collections::HashMap;
use std::num::NonZeroUsize;
use web_sys::console;

/// Cache key for rendered pages
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>h, <PERSON><PERSON>Eq, Eq)]
struct CacheKey {
    page_num: u32,
    scale_int: u32, // Scale as integer (scale * 1000) for precise comparison
}

impl CacheKey {
    fn new(page_num: u32, scale: f32) -> Self {
        Self {
            page_num,
            scale_int: (scale * 1000.0) as u32,
        }
    }
}

/// Document cache for rendered pages with LRU eviction
pub struct DocumentCache {
    /// LRU cache for rendered pages
    page_cache: LruCache<CacheKey, CacheEntry>,
    /// Maximum cache size in bytes
    max_size_bytes: usize,
    /// Current cache size in bytes
    current_size_bytes: usize,
    /// Maximum number of cached pages
    max_pages: usize,
    /// Access statistics
    stats: CacheStats,
}

/// Cache statistics
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
struct CacheStats {
    hits: u64,
    misses: u64,
    evictions: u64,
    total_requests: u64,
}

impl DocumentCache {
    /// Create a new document cache
    pub fn new(max_size_mb: usize, max_pages: usize) -> PDFResult<Self> {
        let capacity = NonZeroUsize::new(max_pages)
            .ok_or_else(|| PDFError::ConfigError("Cache capacity must be greater than 0".to_string()))?;
        
        console::log_1(&format!("Initializing cache: {}MB, {} pages max", max_size_mb, max_pages).into());
        
        Ok(DocumentCache {
            page_cache: LruCache::new(capacity),
            max_size_bytes: max_size_mb * 1024 * 1024,
            current_size_bytes: 0,
            max_pages,
            stats: CacheStats::default(),
        })
    }
    
    /// Store a rendered page in the cache
    pub fn store_page(&mut self, page_num: u32, scale: f32, page: RenderedPage) -> PDFResult<()> {
        let key = CacheKey::new(page_num, scale);
        let page_size = page.image_data.len();
        
        // Check if we need to evict pages to make room
        self.ensure_capacity(page_size)?;
        
        // Create cache entry
        let entry = CacheEntry {
            page,
            access_count: 1,
            last_accessed: js_sys::Date::now() as u64,
        };
        
        // Store in cache
        if let Some(old_entry) = self.page_cache.put(key, entry) {
            // Update size if we replaced an existing entry
            self.current_size_bytes -= old_entry.page.image_data.len();
            self.stats.evictions += 1;
        }
        
        self.current_size_bytes += page_size;
        
        console::log_1(&format!(
            "Cached page {} (scale: {:.2}, size: {}KB, total cache: {}KB)",
            page_num, scale, page_size / 1024, self.current_size_bytes / 1024
        ).into());
        
        Ok(())
    }
    
    /// Retrieve a rendered page from the cache
    pub fn get_page(&mut self, page_num: u32, scale: f32) -> Option<RenderedPage> {
        let key = CacheKey::new(page_num, scale);
        self.stats.total_requests += 1;
        
        if let Some(entry) = self.page_cache.get_mut(&key) {
            // Update access statistics
            entry.access_count += 1;
            entry.last_accessed = js_sys::Date::now() as u64;
            self.stats.hits += 1;
            
            console::log_1(&format!("Cache hit for page {} (scale: {:.2})", page_num, scale).into());
            Some(entry.page.clone())
        } else {
            self.stats.misses += 1;
            console::log_1(&format!("Cache miss for page {} (scale: {:.2})", page_num, scale).into());
            None
        }
    }
    
    /// Check if a page is cached
    pub fn has_page(&self, page_num: u32, scale: f32) -> bool {
        let key = CacheKey::new(page_num, scale);
        self.page_cache.contains(&key)
    }
    
    /// Remove a specific page from cache
    pub fn remove_page(&mut self, page_num: u32, scale: f32) -> bool {
        let key = CacheKey::new(page_num, scale);
        if let Some(entry) = self.page_cache.pop(&key) {
            self.current_size_bytes -= entry.page.image_data.len();
            console::log_1(&format!("Removed page {} from cache", page_num).into());
            true
        } else {
            false
        }
    }
    
    /// Clear all cached pages
    pub fn clear(&mut self) {
        let old_size = self.current_size_bytes;
        let old_count = self.page_cache.len();
        
        self.page_cache.clear();
        self.current_size_bytes = 0;
        
        console::log_1(&format!(
            "Cache cleared: {} pages, {}KB freed",
            old_count, old_size / 1024
        ).into());
    }
    
    /// Get memory usage statistics
    pub fn get_memory_usage(&self) -> MemoryUsage {
        MemoryUsage {
            total_allocated: self.current_size_bytes,
            cache_size: self.current_size_bytes,
            active_pages: self.page_cache.len(),
            cached_pages: self.page_cache.len(),
        }
    }
    
    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStatistics {
        let hit_rate = if self.stats.total_requests > 0 {
            (self.stats.hits as f64 / self.stats.total_requests as f64) * 100.0
        } else {
            0.0
        };
        
        CacheStatistics {
            hits: self.stats.hits,
            misses: self.stats.misses,
            evictions: self.stats.evictions,
            total_requests: self.stats.total_requests,
            hit_rate,
            current_size_bytes: self.current_size_bytes,
            max_size_bytes: self.max_size_bytes,
            cached_pages: self.page_cache.len(),
            max_pages: self.max_pages,
        }
    }
    
    /// Ensure there's enough capacity for a new page
    fn ensure_capacity(&mut self, required_size: usize) -> PDFResult<()> {
        // Check if the single page is too large for the cache
        if required_size > self.max_size_bytes {
            return Err(PDFError::OutOfMemory { size: required_size });
        }
        
        // Evict pages until we have enough space
        while self.current_size_bytes + required_size > self.max_size_bytes {
            if let Some((_, entry)) = self.page_cache.pop_lru() {
                self.current_size_bytes -= entry.page.image_data.len();
                self.stats.evictions += 1;
                console::log_1(&format!(
                    "Evicted page {} to make room (freed {}KB)",
                    entry.page.page_num, entry.page.image_data.len() / 1024
                ).into());
            } else {
                // Cache is empty but still not enough space
                break;
            }
        }
        
        Ok(())
    }
    
    /// Optimize cache by removing least accessed pages
    pub fn optimize(&mut self) {
        let initial_size = self.page_cache.len();
        let target_size = (self.max_pages as f32 * 0.8) as usize; // Keep 80% capacity
        
        if initial_size <= target_size {
            return; // No optimization needed
        }
        
        // Collect pages with their access patterns
        let mut pages_info: Vec<(CacheKey, u32, u64)> = Vec::new();
        
        // We can't iterate and modify LRU cache simultaneously, so we'll use a different approach
        // For now, just remove some LRU pages
        let pages_to_remove = initial_size - target_size;
        
        for _ in 0..pages_to_remove {
            if let Some((_, entry)) = self.page_cache.pop_lru() {
                self.current_size_bytes -= entry.page.image_data.len();
                self.stats.evictions += 1;
            }
        }
        
        console::log_1(&format!(
            "Cache optimized: removed {} pages, {} pages remaining",
            pages_to_remove, self.page_cache.len()
        ).into());
    }
    
    /// Preload pages around a given page number
    pub fn suggest_preload(&self, current_page: u32, total_pages: u32) -> Vec<u32> {
        let mut suggestions = Vec::new();
        
        // Suggest next few pages
        for i in 1..=3 {
            let next_page = current_page + i;
            if next_page <= total_pages {
                suggestions.push(next_page);
            }
        }
        
        // Suggest previous page if not already cached
        if current_page > 1 {
            suggestions.push(current_page - 1);
        }
        
        suggestions
    }
}

/// Cache statistics for monitoring
#[derive(Debug, Clone)]
pub struct CacheStatistics {
    pub hits: u64,
    pub misses: u64,
    pub evictions: u64,
    pub total_requests: u64,
    pub hit_rate: f64,
    pub current_size_bytes: usize,
    pub max_size_bytes: usize,
    pub cached_pages: usize,
    pub max_pages: usize,
}

/// Memory pool for efficient allocation
pub struct MemoryPool {
    pool_size: usize,
    allocated: usize,
    blocks: Vec<Vec<u8>>,
}

impl MemoryPool {
    /// Create a new memory pool
    pub fn new(pool_size: usize) -> Self {
        console::log_1(&format!("Creating memory pool: {}MB", pool_size / (1024 * 1024)).into());
        
        MemoryPool {
            pool_size,
            allocated: 0,
            blocks: Vec::new(),
        }
    }
    
    /// Allocate a block of memory
    pub fn allocate(&mut self, size: usize) -> PDFResult<Vec<u8>> {
        if self.allocated + size > self.pool_size {
            return Err(PDFError::OutOfMemory { size });
        }
        
        let block = vec![0u8; size];
        self.allocated += size;
        self.blocks.push(block.clone());
        
        Ok(block)
    }
    
    /// Deallocate memory (simplified implementation)
    pub fn deallocate(&mut self, size: usize) {
        self.allocated = self.allocated.saturating_sub(size);
    }
    
    /// Get current allocation
    pub fn get_allocated(&self) -> usize {
        self.allocated
    }
    
    /// Get available memory
    pub fn get_available(&self) -> usize {
        self.pool_size - self.allocated
    }
}
