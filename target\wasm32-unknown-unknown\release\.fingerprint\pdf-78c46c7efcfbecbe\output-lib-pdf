{"$message_type": "future_incompat", "future_incompat_report": [{"diagnostic": {"$message_type": "diagnostic", "message": "`Rect` is ambiguous", "code": {"code": "ambiguous_glob_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 209, "byte_end": 213, "line_start": 9, "line_end": 9, "column_start": 27, "column_end": 31, "is_primary": true, "text": [{"text": "    pub media_box: Option<Rect>,", "highlight_start": 27, "highlight_end": 31}], "label": "ambiguous name", "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "ambiguous because of multiple glob imports of a name in the same module", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 4, "byte_end": 20, "line_start": 1, "line_end": 1, "column_start": 5, "column_end": 21, "is_primary": true, "text": [{"text": "use crate::object::*;", "highlight_start": 5, "highlight_end": 21}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could also refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 26, "byte_end": 43, "line_start": 2, "line_end": 2, "column_start": 5, "column_end": 22, "is_primary": true, "text": [{"text": "use crate::content::*;", "highlight_start": 5, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:9:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub media_box: Option<Rect>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "`Rect` is ambiguous", "code": {"code": "ambiguous_glob_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 241, "byte_end": 245, "line_start": 10, "line_end": 10, "column_start": 26, "column_end": 30, "is_primary": true, "text": [{"text": "    pub crop_box: Option<Rect>,", "highlight_start": 26, "highlight_end": 30}], "label": "ambiguous name", "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "ambiguous because of multiple glob imports of a name in the same module", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 4, "byte_end": 20, "line_start": 1, "line_end": 1, "column_start": 5, "column_end": 21, "is_primary": true, "text": [{"text": "use crate::object::*;", "highlight_start": 5, "highlight_end": 21}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could also refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 26, "byte_end": 43, "line_start": 2, "line_end": 2, "column_start": 5, "column_end": 22, "is_primary": true, "text": [{"text": "use crate::content::*;", "highlight_start": 5, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:10:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub crop_box: Option<Rect>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "`Rect` is ambiguous", "code": {"code": "ambiguous_glob_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 273, "byte_end": 277, "line_start": 11, "line_end": 11, "column_start": 26, "column_end": 30, "is_primary": true, "text": [{"text": "    pub trim_box: Option<Rect>,", "highlight_start": 26, "highlight_end": 30}], "label": "ambiguous name", "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "ambiguous because of multiple glob imports of a name in the same module", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 4, "byte_end": 20, "line_start": 1, "line_end": 1, "column_start": 5, "column_end": 21, "is_primary": true, "text": [{"text": "use crate::object::*;", "highlight_start": 5, "highlight_end": 21}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could also refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 26, "byte_end": 43, "line_start": 2, "line_end": 2, "column_start": 5, "column_end": 22, "is_primary": true, "text": [{"text": "use crate::content::*;", "highlight_start": 5, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:11:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub trim_box: Option<Rect>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\n"}}, {"diagnostic": {"$message_type": "diagnostic", "message": "`Rect` is ambiguous", "code": {"code": "ambiguous_glob_imports", "explanation": null}, "level": "warning", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 1203, "byte_end": 1207, "line_start": 39, "line_end": 39, "column_start": 31, "column_end": 35, "is_primary": true, "text": [{"text": "        self.media_box = Some(Rect {", "highlight_start": 31, "highlight_end": 35}], "label": "ambiguous name", "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "ambiguous because of multiple glob imports of a name in the same module", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 4, "byte_end": 20, "line_start": 1, "line_end": 1, "column_start": 5, "column_end": 21, "is_primary": true, "text": [{"text": "use crate::object::*;", "highlight_start": 5, "highlight_end": 21}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "`Rect` could also refer to the struct imported here", "code": null, "level": "note", "spans": [{"file_name": "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs", "byte_start": 26, "byte_end": 43, "line_start": 2, "line_end": 2, "column_start": 5, "column_end": 22, "is_primary": true, "text": [{"text": "use crate::content::*;", "highlight_start": 5, "highlight_end": 22}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "consider adding an explicit import of `Rect` to disambiguate", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:39:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.media_box = Some(Rect {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n\n"}}]}