{"rustc": 7868289081541623310, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 6418093726722172150, "path": 7326161735121813010, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\memchr-f4c5880e3bf63f19\\dep-lib-memchr", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}