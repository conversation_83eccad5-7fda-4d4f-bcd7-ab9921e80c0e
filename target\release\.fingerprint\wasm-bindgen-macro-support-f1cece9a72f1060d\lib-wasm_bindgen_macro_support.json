{"rustc": 7868289081541623310, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 16199798871480566777, "path": 15322381914801151561, "deps": [[373107762698212489, "proc_macro2", false, 2539326917690095710], [7826122624549889939, "wasm_bindgen_shared", false, 1660031222898199134], [10859219585559253335, "wasm_bindgen_backend", false, 13925879660855232966], [17332570067994900305, "syn", false, 11438009645782614110], [17990358020177143287, "quote", false, 16806568807362843329]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wasm-bindgen-macro-support-f1cece9a72f1060d\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}