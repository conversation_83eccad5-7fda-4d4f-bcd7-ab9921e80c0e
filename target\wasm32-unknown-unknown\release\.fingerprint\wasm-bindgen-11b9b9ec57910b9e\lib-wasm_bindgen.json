{"rustc": 7868289081541623310, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 16333205938936660957, "path": 15176666018012730968, "deps": [[3722963349756955755, "once_cell", false, 15574632292824914997], [3894621559386490394, "wasm_bindgen_macro", false, 16180532616047270367], [7826122624549889939, "wasm_bindgen_shared", false, 15693925606104743536], [7843059260364151289, "cfg_if", false, 10459516428561978112], [14156967978702956262, "rustversion", false, 372091198286217238], [17362525766049117937, "build_script_build", false, 10990550270621496675]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\wasm-bindgen-11b9b9ec57910b9e\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}