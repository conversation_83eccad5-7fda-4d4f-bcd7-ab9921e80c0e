use crate::types::*;
use crate::error::{PDFError, PDFResult};

use wasm_bindgen::prelude::*;
use web_sys::{console, Performance, Window};
use std::collections::HashMap;

/// Utility functions for the PDF engine

/// Performance monitoring utilities
pub struct PerformanceMonitor {
    start_times: HashMap<String, f64>,
    performance: Option<Performance>,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        let performance = web_sys::window()
            .and_then(|w| w.performance());
        
        Self {
            start_times: HashMap::new(),
            performance,
        }
    }
    
    pub fn start_timer(&mut self, name: &str) {
        if let Some(ref perf) = self.performance {
            self.start_times.insert(name.to_string(), perf.now());
        }
    }
    
    pub fn end_timer(&mut self, name: &str) -> Option<f64> {
        if let Some(ref perf) = self.performance {
            if let Some(start_time) = self.start_times.remove(name) {
                let duration = perf.now() - start_time;
                console::log_1(&format!("Timer '{}': {:.2}ms", name, duration).into());
                return Some(duration);
            }
        }
        None
    }
    
    pub fn measure<T, F>(&self, name: &str, f: F) -> T 
    where F: FnOnce() -> T 
    {
        let start = self.performance.as_ref().map(|p| p.now()).unwrap_or(0.0);
        let result = f();
        let end = self.performance.as_ref().map(|p| p.now()).unwrap_or(0.0);
        
        console::log_1(&format!("Operation '{}': {:.2}ms", name, end - start).into());
        result
    }
}

/// Memory usage utilities
pub struct MemoryTracker {
    allocations: HashMap<String, usize>,
    total_allocated: usize,
}

impl MemoryTracker {
    pub fn new() -> Self {
        Self {
            allocations: HashMap::new(),
            total_allocated: 0,
        }
    }
    
    pub fn track_allocation(&mut self, name: &str, size: usize) {
        self.allocations.insert(name.to_string(), size);
        self.total_allocated += size;
        
        console::log_1(&format!(
            "Memory allocated '{}': {}KB (total: {}KB)",
            name, size / 1024, self.total_allocated / 1024
        ).into());
    }
    
    pub fn track_deallocation(&mut self, name: &str) {
        if let Some(size) = self.allocations.remove(name) {
            self.total_allocated = self.total_allocated.saturating_sub(size);
            
            console::log_1(&format!(
                "Memory deallocated '{}': {}KB (total: {}KB)",
                name, size / 1024, self.total_allocated / 1024
            ).into());
        }
    }
    
    pub fn get_total_allocated(&self) -> usize {
        self.total_allocated
    }
    
    pub fn get_allocation(&self, name: &str) -> Option<usize> {
        self.allocations.get(name).copied()
    }
}

/// Coordinate transformation utilities
pub struct CoordinateTransform;

impl CoordinateTransform {
    /// Convert PDF coordinates to screen coordinates
    pub fn pdf_to_screen(pdf_point: Point, page_size: PageSize, canvas_size: (u32, u32), scale: f32) -> Point {
        let scale_x = (canvas_size.0 as f32) / (page_size.width * scale);
        let scale_y = (canvas_size.1 as f32) / (page_size.height * scale);
        
        Point {
            x: pdf_point.x * scale_x,
            y: (page_size.height - pdf_point.y) * scale_y, // Flip Y axis
        }
    }
    
    /// Convert screen coordinates to PDF coordinates
    pub fn screen_to_pdf(screen_point: Point, page_size: PageSize, canvas_size: (u32, u32), scale: f32) -> Point {
        let scale_x = (page_size.width * scale) / (canvas_size.0 as f32);
        let scale_y = (page_size.height * scale) / (canvas_size.1 as f32);
        
        Point {
            x: screen_point.x * scale_x,
            y: page_size.height - (screen_point.y * scale_y), // Flip Y axis
        }
    }
    
    /// Apply rotation to a point
    pub fn rotate_point(point: Point, rotation: i32, page_size: PageSize) -> Point {
        let angle = (rotation as f32).to_radians();
        let cos_a = angle.cos();
        let sin_a = angle.sin();
        
        let center_x = page_size.width / 2.0;
        let center_y = page_size.height / 2.0;
        
        // Translate to origin
        let x = point.x - center_x;
        let y = point.y - center_y;
        
        // Rotate
        let rotated_x = x * cos_a - y * sin_a;
        let rotated_y = x * sin_a + y * cos_a;
        
        // Translate back
        Point {
            x: rotated_x + center_x,
            y: rotated_y + center_y,
        }
    }
}

/// Text processing utilities
pub struct TextUtils;

impl TextUtils {
    /// Normalize text for searching
    pub fn normalize_for_search(text: &str) -> String {
        text.to_lowercase()
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace())
            .collect()
    }
    
    /// Extract words from text
    pub fn extract_words(text: &str) -> Vec<String> {
        text.split_whitespace()
            .map(|word| word.trim_matches(|c: char| !c.is_alphanumeric()))
            .filter(|word| !word.is_empty())
            .map(|word| word.to_string())
            .collect()
    }
    
    /// Calculate text similarity (simple implementation)
    pub fn calculate_similarity(text1: &str, text2: &str) -> f32 {
        let words1 = Self::extract_words(&Self::normalize_for_search(text1));
        let words2 = Self::extract_words(&Self::normalize_for_search(text2));
        
        if words1.is_empty() && words2.is_empty() {
            return 1.0;
        }
        
        if words1.is_empty() || words2.is_empty() {
            return 0.0;
        }
        
        let mut common_words = 0;
        for word1 in &words1 {
            if words2.contains(word1) {
                common_words += 1;
            }
        }
        
        (common_words as f32) / (words1.len().max(words2.len()) as f32)
    }
    
    /// Find text positions in a string
    pub fn find_text_positions(haystack: &str, needle: &str) -> Vec<(usize, usize)> {
        let normalized_haystack = Self::normalize_for_search(haystack);
        let normalized_needle = Self::normalize_for_search(needle);
        
        let mut positions = Vec::new();
        let mut start = 0;
        
        while let Some(pos) = normalized_haystack[start..].find(&normalized_needle) {
            let absolute_pos = start + pos;
            positions.push((absolute_pos, absolute_pos + normalized_needle.len()));
            start = absolute_pos + 1;
        }
        
        positions
    }
}

/// Image processing utilities
pub struct ImageUtils;

impl ImageUtils {
    /// Convert RGBA to RGB
    pub fn rgba_to_rgb(rgba_data: &[u8]) -> Vec<u8> {
        let mut rgb_data = Vec::with_capacity((rgba_data.len() / 4) * 3);
        
        for chunk in rgba_data.chunks_exact(4) {
            rgb_data.push(chunk[0]); // R
            rgb_data.push(chunk[1]); // G
            rgb_data.push(chunk[2]); // B
            // Skip alpha channel
        }
        
        rgb_data
    }
    
    /// Convert RGB to RGBA (add alpha channel)
    pub fn rgb_to_rgba(rgb_data: &[u8], alpha: u8) -> Vec<u8> {
        let mut rgba_data = Vec::with_capacity((rgb_data.len() / 3) * 4);
        
        for chunk in rgb_data.chunks_exact(3) {
            rgba_data.push(chunk[0]); // R
            rgba_data.push(chunk[1]); // G
            rgba_data.push(chunk[2]); // B
            rgba_data.push(alpha);    // A
        }
        
        rgba_data
    }
    
    /// Apply grayscale conversion
    pub fn to_grayscale(rgba_data: &[u8]) -> Vec<u8> {
        let mut gray_data = Vec::with_capacity(rgba_data.len());
        
        for chunk in rgba_data.chunks_exact(4) {
            let r = chunk[0] as f32;
            let g = chunk[1] as f32;
            let b = chunk[2] as f32;
            let a = chunk[3];
            
            // Use luminance formula
            let gray = (0.299 * r + 0.587 * g + 0.114 * b) as u8;
            
            gray_data.push(gray); // R
            gray_data.push(gray); // G
            gray_data.push(gray); // B
            gray_data.push(a);    // A
        }
        
        gray_data
    }
    
    /// Resize image data (simple nearest neighbor)
    pub fn resize_nearest_neighbor(
        data: &[u8], 
        old_width: u32, 
        old_height: u32, 
        new_width: u32, 
        new_height: u32
    ) -> Vec<u8> {
        let mut resized = Vec::with_capacity((new_width * new_height * 4) as usize);
        
        let x_ratio = old_width as f32 / new_width as f32;
        let y_ratio = old_height as f32 / new_height as f32;
        
        for y in 0..new_height {
            for x in 0..new_width {
                let src_x = (x as f32 * x_ratio) as u32;
                let src_y = (y as f32 * y_ratio) as u32;
                
                let src_index = ((src_y * old_width + src_x) * 4) as usize;
                
                if src_index + 3 < data.len() {
                    resized.push(data[src_index]);     // R
                    resized.push(data[src_index + 1]); // G
                    resized.push(data[src_index + 2]); // B
                    resized.push(data[src_index + 3]); // A
                } else {
                    resized.extend_from_slice(&[255, 255, 255, 255]); // White pixel
                }
            }
        }
        
        resized
    }
}

/// Validation utilities
pub struct Validator;

impl Validator {
    /// Validate page number
    pub fn validate_page_number(page_num: u32, total_pages: u32) -> PDFResult<()> {
        if page_num == 0 || page_num > total_pages {
            Err(PDFError::InvalidPageNumber { page: page_num, total_pages })
        } else {
            Ok(())
        }
    }
    
    /// Validate scale factor
    pub fn validate_scale(scale: f32) -> PDFResult<f32> {
        if scale <= 0.0 {
            Err(PDFError::ConfigError("Scale must be positive".to_string()))
        } else if scale > 10.0 {
            Err(PDFError::ConfigError("Scale too large (max 10.0)".to_string()))
        } else {
            Ok(scale)
        }
    }
    
    /// Validate canvas ID
    pub fn validate_canvas_id(canvas_id: &str) -> PDFResult<()> {
        if canvas_id.is_empty() {
            Err(PDFError::ConfigError("Canvas ID cannot be empty".to_string()))
        } else {
            Ok(())
        }
    }
    
    /// Validate PDF data
    pub fn validate_pdf_data(data: &[u8]) -> PDFResult<()> {
        if data.is_empty() {
            return Err(PDFError::InvalidFormat);
        }
        
        // Check PDF header
        if data.len() >= 4 && &data[0..4] == b"%PDF" {
            Ok(())
        } else {
            Err(PDFError::InvalidFormat)
        }
    }
}

/// Logging utilities
pub struct Logger;

impl Logger {
    pub fn info(message: &str) {
        console::log_1(&format!("[INFO] {}", message).into());
    }
    
    pub fn warn(message: &str) {
        console::warn_1(&format!("[WARN] {}", message).into());
    }
    
    pub fn error(message: &str) {
        console::error_1(&format!("[ERROR] {}", message).into());
    }
    
    pub fn debug(message: &str) {
        #[cfg(debug_assertions)]
        console::log_1(&format!("[DEBUG] {}", message).into());
    }
}
