{"name": "pdf-render-engine", "version": "0.1.0", "description": "High-performance PDF rendering engine with WebAssembly", "main": "bundler/pdf_render_engine.js", "module": "web/pdf_render_engine.js", "types": "bundler/pdf_render_engine.d.ts", "files": ["bundler/", "web/", "nodejs/", "README.md"], "keywords": ["pdf", "rendering", "webassembly", "wasm", "rust"], "author": "PDF Render Engine Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/pdf-render-engine"}, "engines": {"node": ">=14.0.0"}}