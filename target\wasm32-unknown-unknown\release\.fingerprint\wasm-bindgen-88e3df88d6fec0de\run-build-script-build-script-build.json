{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17362525766049117937, "build_script_build", false, 5305317993951419251], [7826122624549889939, "build_script_build", false, 10018589830576237869]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\wasm-bindgen-88e3df88d6fec0de\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}