{"rustc": 7868289081541623310, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[15826188163127377936, "build_script_build", false, 13710759803027794327]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown\\release\\build\\matrixmultiply-ab279995bff7816e\\output", "paths": ["build.rs"]}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 0, "compile_kind": 0}