{"rustc": 7868289081541623310, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 4913466754190795764, "profile": 7645849607973802078, "path": 5240653156372497212, "deps": [[3722963349756955755, "once_cell", false, 15574632292824914997], [17362525766049117937, "wasm_bindgen", false, 1611265920349769740]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\js-sys-913ab2ebb518e5ff\\dep-lib-js_sys", "checksum": false}}], "rustflags": ["-C", "target-feature=+simd128", "-C", "target-feature=+bulk-memory"], "config": 2069994364910194474, "compile_kind": 14682669768258224367}