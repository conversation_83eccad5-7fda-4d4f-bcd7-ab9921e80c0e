use crate::types::*;
use crate::error::{PDFError, PDFResult};

// PDF parsing libraries
#[cfg(feature = "pdfium-parser")]
use pdfium_render::prelude::*;

use lopdf::Document as LopdfDocument;
#[cfg(feature = "pdf-rs-parser")]
use pdf::file::File as PdfFile;
use pdf_extract;

use wasm_bindgen_futures::JsFuture;
use web_sys::console;
use std::io::Cursor;

/// Parser type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ParserType {
    PDFium,
    Lopdf,
    PdfRs,
}

/// Unified PDF parser interface
pub struct PDFParser {
    parser_type: ParserType,
    #[cfg(feature = "pdfium-parser")]
    pdfium_doc: Option<PdfDocument<'static>>,
    lopdf_doc: Option<LopdfDocument>,
    #[cfg(feature = "pdf-rs-parser")]
    pdfrs_doc: Option<PdfFile<Vec<u8>, pdf::object::ObjStm, pdf::object::XRef>>,
    document_data: Vec<u8>,
}

impl PDFParser {
    /// Create a new parser of the specified type
    pub fn new(parser_type: ParserType) -> PDFResult<Self> {
        console::log_1(&format!("Initializing {:?} parser", parser_type).into());

        Ok(PDFParser {
            parser_type,
            #[cfg(feature = "pdfium-parser")]
            pdfium_doc: None,
            lopdf_doc: None,
            #[cfg(feature = "pdf-rs-parser")]
            pdfrs_doc: None,
            document_data: Vec::new(),
        })
    }
    
    /// Get the parser type
    pub fn get_type(&self) -> ParserType {
        self.parser_type
    }
    
    /// Load a PDF document with hybrid approach
    pub async fn load_document(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        // Store document data
        self.document_data = data.to_vec();

        // Validate PDF format
        if data.len() < 4 || &data[0..4] != b"%PDF" {
            return Err(PDFError::InvalidFormat);
        }

        // Try parsers in order of preference
        match self.parser_type {
            ParserType::PDFium => {
                #[cfg(feature = "pdfium-parser")]
                {
                    match self.load_with_pdfium(data).await {
                        Ok(info) => return Ok(info),
                        Err(e) => {
                            console::warn_1(&format!("PDFium failed: {}, trying fallback", e).into());
                            // Fall through to lopdf
                        }
                    }
                }
                // If PDFium not available or failed, try lopdf
                self.load_with_lopdf(data).await
            },
            ParserType::Lopdf => self.load_with_lopdf(data).await,
            ParserType::PdfRs => {
                #[cfg(feature = "pdf-rs-parser")]
                {
                    self.load_with_pdfrs(data).await
                }
                #[cfg(not(feature = "pdf-rs-parser"))]
                {
                    // Fallback to lopdf if pdf-rs not available
                    self.load_with_lopdf(data).await
                }
            },
        }
    }
    
    /// Load document with PDFium (when available)
    #[cfg(feature = "pdfium-parser")]
    async fn load_with_pdfium(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        // Initialize PDFium
        let pdfium = Pdfium::new(
            Pdfium::bind_to_library(Pdfium::pdfium_platform_library_name_at_path("./"))
                .or_else(|_| Pdfium::bind_to_system_library())
                .map_err(|e| PDFError::ParseError(format!("Failed to bind PDFium: {}", e)))?
        );

        // Load document
        let document = pdfium.load_pdf_from_bytes(data, None)
            .map_err(|e| PDFError::ParseError(format!("PDFium load failed: {}", e)))?;

        // Extract document information
        let page_count = document.pages().len() as u32;
        let metadata = document.metadata();

        let document_info = DocumentInfo::new(
            page_count,
            metadata.title().map(|s| s.to_string()),
            metadata.author().map(|s| s.to_string()),
            metadata.subject().map(|s| s.to_string()),
            metadata.creator().map(|s| s.to_string()),
            metadata.producer().map(|s| s.to_string()),
            metadata.creation_date().map(|d| d.to_string()),
            metadata.modification_date().map(|d| d.to_string()),
            document.is_encrypted(),
            self.extract_pdfium_permissions(&document),
        );

        self.pdfium_doc = Some(document);
        console::log_1(&format!("PDFium loaded document with {} pages", page_count).into());
        Ok(document_info)
    }
    
    /// Load document with lopdf (pure Rust fallback)
    async fn load_with_lopdf(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        let document = LopdfDocument::load_mem(data)
            .map_err(|e| PDFError::ParseError(format!("lopdf load failed: {}", e)))?;

        // Extract basic information
        let page_count = document.get_pages().len() as u32;

        // Extract metadata from document info dictionary
        let (title, author, subject, creator, producer, creation_date, modification_date) =
            self.extract_lopdf_metadata(&document);

        // Check if document is encrypted
        let encrypted = document.is_encrypted();

        let document_info = DocumentInfo::new(
            page_count,
            title,
            author,
            subject,
            creator,
            producer,
            creation_date,
            modification_date,
            encrypted,
            self.extract_lopdf_permissions(&document),
        );

        self.lopdf_doc = Some(document);
        console::log_1(&format!("lopdf loaded document with {} pages", page_count).into());
        Ok(document_info)
    }
    
    /// Load document with pdf-rs (alternative Rust parser)
    #[cfg(feature = "pdf-rs-parser")]
    async fn load_with_pdfrs(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        let file = PdfFile::from_data(data.to_vec(), pdf::object::ObjStm::default(), pdf::object::XRef::default())
            .map_err(|e| PDFError::ParseError(format!("pdf-rs load failed: {}", e)))?;

        let page_count = file.num_pages() as u32;

        // Extract metadata from info dictionary
        let info = file.trailer.info_dict.as_ref();
        let title = info.and_then(|i| i.title.as_ref()).map(|s| s.to_string());
        let author = info.and_then(|i| i.author.as_ref()).map(|s| s.to_string());
        let subject = info.and_then(|i| i.subject.as_ref()).map(|s| s.to_string());
        let creator = info.and_then(|i| i.creator.as_ref()).map(|s| s.to_string());
        let producer = info.and_then(|i| i.producer.as_ref()).map(|s| s.to_string());

        // Extract dates if available
        let creation_date = info.and_then(|i| i.creation_date.as_ref()).map(|d| d.to_string());
        let modification_date = info.and_then(|i| i.modification_date.as_ref()).map(|d| d.to_string());

        let document_info = DocumentInfo {
            page_count,
            title,
            author,
            subject,
            creator,
            producer,
            creation_date,
            modification_date,
            encrypted: file.is_encrypted(),
            permissions: self.extract_pdfrs_permissions(&file),
        };

        self.pdfrs_doc = Some(file);
        console::log_1(&format!("pdf-rs loaded document with {} pages", page_count).into());
        Ok(document_info)
    }
    
    /// Get page size
    pub fn get_page_size(&self, page_num: u32) -> PDFResult<PageSize> {
        match self.parser_type {
            ParserType::PDFium => {
                #[cfg(feature = "pdfium-parser")]
                {
                    self.get_pdfium_page_size(page_num)
                }
                #[cfg(not(feature = "pdfium-parser"))]
                {
                    // Fallback to lopdf if PDFium not available
                    self.get_lopdf_page_size(page_num)
                }
            },
            ParserType::Lopdf => self.get_lopdf_page_size(page_num),
            ParserType::PdfRs => self.get_pdfrs_page_size(page_num),
        }
    }
    
    /// Render a page to image data
    pub async fn render_page(&self, page_num: u32, scale: f32) -> PDFResult<RenderedPage> {
        match self.parser_type {
            ParserType::PDFium => {
                #[cfg(feature = "pdfium-parser")]
                {
                    self.render_pdfium_page(page_num, scale).await
                }
                #[cfg(not(feature = "pdfium-parser"))]
                {
                    // Fallback to lopdf if PDFium not available
                    self.render_lopdf_page(page_num, scale).await
                }
            },
            ParserType::Lopdf => self.render_lopdf_page(page_num, scale).await,
            ParserType::PdfRs => self.render_pdfrs_page(page_num, scale).await,
        }
    }
    
    /// Extract text from a page
    pub fn extract_text(&self, page_num: u32) -> PDFResult<String> {
        match self.parser_type {
            ParserType::PDFium => {
                #[cfg(feature = "pdfium-parser")]
                {
                    self.extract_pdfium_text(page_num)
                }
                #[cfg(not(feature = "pdfium-parser"))]
                {
                    // Fallback to lopdf if PDFium not available
                    self.extract_lopdf_text(page_num)
                }
            },
            ParserType::Lopdf => self.extract_lopdf_text(page_num),
            ParserType::PdfRs => {
                #[cfg(feature = "pdf-rs-parser")]
                {
                    self.extract_pdfrs_text(page_num)
                }
                #[cfg(not(feature = "pdf-rs-parser"))]
                {
                    // Fallback to lopdf if pdf-rs not available
                    self.extract_lopdf_text(page_num)
                }
            },
        }
    }
    
    /// Search for text in the document
    pub fn search_text(&self, query: &str) -> PDFResult<Vec<SearchResult>> {
        match self.parser_type {
            ParserType::PDFium => {
                #[cfg(feature = "pdfium-parser")]
                {
                    self.search_pdfium_text(query)
                }
                #[cfg(not(feature = "pdfium-parser"))]
                {
                    // Fallback to lopdf if PDFium not available
                    self.search_lopdf_text(query)
                }
            },
            ParserType::Lopdf => self.search_lopdf_text(query),
            ParserType::PdfRs => self.search_pdfrs_text(query),
        }
    }
    
    // PDFium-specific implementations
    
    #[cfg(feature = "pdfium-parser")]
    fn get_pdfium_page_size(&self, page_num: u32) -> PDFResult<PageSize> {
        let doc = self.pdfium_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("PDFium document not loaded".to_string()))?;
        
        let page = doc.pages().get((page_num - 1) as u16)
            .map_err(|e| PDFError::InvalidPageNumber { page: page_num, total_pages: doc.pages().len() as u32 })?;
        
        let size = page.size();
        Ok(PageSize {
            width: size.width,
            height: size.height,
            rotation: page.rotation().as_degrees(),
        })
    }
    
    #[cfg(feature = "pdfium-parser")]
    async fn render_pdfium_page(&self, page_num: u32, scale: f32) -> PDFResult<RenderedPage> {
        let doc = self.pdfium_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("PDFium document not loaded".to_string()))?;
        
        let page = doc.pages().get((page_num - 1) as u16)
            .map_err(|e| PDFError::RenderError { 
                page: page_num, 
                reason: format!("Page not found: {}", e) 
            })?;
        
        let size = page.size();
        let width = (size.width * scale) as u32;
        let height = (size.height * scale) as u32;
        
        // Render page to bitmap
        let bitmap = page.render()
            .map_err(|e| PDFError::RenderError { 
                page: page_num, 
                reason: format!("Render failed: {}", e) 
            })?
            .as_image()
            .as_rgba8()
            .ok_or_else(|| PDFError::RenderError { 
                page: page_num, 
                reason: "Failed to convert to RGBA8".to_string() 
            })?;
        
        Ok(RenderedPage {
            page_num,
            width,
            height,
            scale,
            image_data: bitmap.to_vec(),
            timestamp: js_sys::Date::now() as u64,
        })
    }
    
    #[cfg(feature = "pdfium-parser")]
    fn extract_pdfium_text(&self, page_num: u32) -> PDFResult<String> {
        #[cfg(feature = "pdfium-parser")]
        {
            let doc = self.pdfium_doc.as_ref()
                .ok_or_else(|| PDFError::ConfigError("PDFium document not loaded".to_string()))?;
        }
        #[cfg(not(feature = "pdfium-parser"))]
        {
            return Err(PDFError::ConfigError("PDFium not available".to_string()));
        }
        
        let page = doc.pages().get((page_num - 1) as u16)
            .map_err(|e| PDFError::InvalidPageNumber { page: page_num, total_pages: doc.pages().len() as u32 })?;
        
        Ok(page.text().all())
    }
    
    #[cfg(feature = "pdfium-parser")]
    fn search_pdfium_text(&self, query: &str) -> PDFResult<Vec<SearchResult>> {
        let doc = self.pdfium_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("PDFium document not loaded".to_string()))?;
        
        let mut results = Vec::new();
        
        for (page_index, page) in doc.pages().iter().enumerate() {
            let page_text = page.text().all();
            if page_text.to_lowercase().contains(&query.to_lowercase()) {
                // Simple text search - could be enhanced with position information
                results.push(SearchResult {
                    page_num: (page_index + 1) as u32,
                    text: page_text,
                    bounds: vec![], // TODO: Implement bounds detection
                });
            }
        }
        
        Ok(results)
    }
    
    #[cfg(feature = "pdfium-parser")]
    fn extract_pdfium_permissions(&self, document: &PdfDocument) -> DocumentPermissions {
        // Extract permissions from PDFium document
        // This is a simplified implementation
        DocumentPermissions {
            can_print: !document.is_encrypted() || true, // Simplified
            can_modify: !document.is_encrypted() || true,
            can_copy: !document.is_encrypted() || true,
            can_add_notes: !document.is_encrypted() || true,
            can_fill_forms: !document.is_encrypted() || true,
            can_extract_for_accessibility: true,
            can_assemble: !document.is_encrypted() || true,
            can_print_high_quality: !document.is_encrypted() || true,
        }
    }
    
    // Placeholder implementations for lopdf and pdf-rs
    // These would need to be implemented based on the specific capabilities of each library
    
    fn get_lopdf_page_size(&self, page_num: u32) -> PDFResult<PageSize> {
        // Placeholder implementation
        Ok(PageSize {
            width: 612.0,  // Standard letter size
            height: 792.0,
            rotation: 0,
        })
    }
    
    async fn render_lopdf_page(&self, page_num: u32, scale: f32) -> PDFResult<RenderedPage> {
        // Placeholder - lopdf doesn't have built-in rendering
        // Would need to integrate with a rendering library
        Err(PDFError::UnsupportedFeature { 
            feature: "lopdf rendering not implemented".to_string() 
        })
    }
    
    fn extract_lopdf_text(&self, page_num: u32) -> PDFResult<String> {
        let document = self.lopdf_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("lopdf document not loaded".to_string()))?;

        // Use pdf-extract for text extraction
        match pdf_extract::extract_text_from_mem(&self.document_data) {
            Ok(text) => {
                // This extracts all text - we'd need to implement page-specific extraction
                // For now, return a portion based on page number
                let lines: Vec<&str> = text.lines().collect();
                let lines_per_page = lines.len().max(1) / document.get_pages().len().max(1);
                let start_line = ((page_num - 1) as usize) * lines_per_page;
                let end_line = (start_line + lines_per_page).min(lines.len());

                if start_line < lines.len() {
                    Ok(lines[start_line..end_line].join("\n"))
                } else {
                    Ok(String::new())
                }
            },
            Err(e) => {
                console::warn_1(&format!("pdf-extract failed: {}", e).into());
                // Fallback: try to extract text manually from lopdf
                self.extract_lopdf_text_manual(page_num)
            }
        }
    }

    /// Manual text extraction from lopdf (fallback)
    fn extract_lopdf_text_manual(&self, page_num: u32) -> PDFResult<String> {
        let document = self.lopdf_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("lopdf document not loaded".to_string()))?;

        let pages = document.get_pages();
        let page_id = pages.get(&(page_num as u32))
            .ok_or_else(|| PDFError::InvalidPageNumber {
                page: page_num,
                total_pages: pages.len() as u32
            })?;

        // This is a simplified text extraction
        // In a real implementation, we'd parse the content streams
        Ok(format!("Text from page {} (lopdf manual extraction)", page_num))
    }
    
    fn search_lopdf_text(&self, query: &str) -> PDFResult<Vec<SearchResult>> {
        Ok(vec![])
    }
    
    /// Extract metadata from lopdf document
    fn extract_lopdf_metadata(&self, document: &LopdfDocument) -> (Option<String>, Option<String>, Option<String>, Option<String>, Option<String>, Option<String>, Option<String>) {
        use lopdf::Object;

        // Try to get the document info dictionary
        let mut title = None;
        let mut author = None;
        let mut subject = None;
        let mut creator = None;
        let mut producer = None;
        let mut creation_date = None;
        let mut modification_date = None;

        if let Ok(trailer) = document.trailer.get(b"Info") {
            if let Ok(info_ref) = trailer.as_reference() {
                if let Ok(info_obj) = document.get_object(info_ref) {
                    if let Object::Dictionary(info_dict) = info_obj {
                        // Extract title
                        if let Ok(title_obj) = info_dict.get(b"Title") {
                            if let Ok(title_str) = title_obj.as_str() {
                                title = Some(String::from_utf8_lossy(title_str).to_string());
                            }
                        }

                        // Extract author
                        if let Ok(author_obj) = info_dict.get(b"Author") {
                            if let Ok(author_str) = author_obj.as_str() {
                                author = Some(String::from_utf8_lossy(author_str).to_string());
                            }
                        }

                        // Extract subject
                        if let Ok(subject_obj) = info_dict.get(b"Subject") {
                            if let Ok(subject_str) = subject_obj.as_str() {
                                subject = Some(String::from_utf8_lossy(subject_str).to_string());
                            }
                        }

                        // Extract creator
                        if let Ok(creator_obj) = info_dict.get(b"Creator") {
                            if let Ok(creator_str) = creator_obj.as_str() {
                                creator = Some(String::from_utf8_lossy(creator_str).to_string());
                            }
                        }

                        // Extract producer
                        if let Ok(producer_obj) = info_dict.get(b"Producer") {
                            if let Ok(producer_str) = producer_obj.as_str() {
                                producer = Some(String::from_utf8_lossy(producer_str).to_string());
                            }
                        }

                        // Extract creation date
                        if let Ok(creation_obj) = info_dict.get(b"CreationDate") {
                            if let Ok(creation_str) = creation_obj.as_str() {
                                creation_date = Some(String::from_utf8_lossy(creation_str).to_string());
                            }
                        }

                        // Extract modification date
                        if let Ok(mod_obj) = info_dict.get(b"ModDate") {
                            if let Ok(mod_str) = mod_obj.as_str() {
                                modification_date = Some(String::from_utf8_lossy(mod_str).to_string());
                            }
                        }
                    }
                }
            }
        }

        (title, author, subject, creator, producer, creation_date, modification_date)
    }

    /// Extract permissions from lopdf document
    fn extract_lopdf_permissions(&self, document: &LopdfDocument) -> DocumentPermissions {
        // Default permissions for unencrypted documents
        if !document.is_encrypted() {
            return DocumentPermissions {
                can_print: true,
                can_modify: true,
                can_copy: true,
                can_add_notes: true,
                can_fill_forms: true,
                can_extract_for_accessibility: true,
                can_assemble: true,
                can_print_high_quality: true,
            };
        }

        // For encrypted documents, we'd need to check the encryption dictionary
        // This is a simplified implementation
        DocumentPermissions {
            can_print: false,
            can_modify: false,
            can_copy: false,
            can_add_notes: false,
            can_fill_forms: false,
            can_extract_for_accessibility: true, // Usually allowed
            can_assemble: false,
            can_print_high_quality: false,
        }
    }

    /// Extract permissions from pdf-rs document
    #[cfg(feature = "pdf-rs-parser")]
    fn extract_pdfrs_permissions(&self, file: &PdfFile<Vec<u8>, pdf::object::ObjStm, pdf::object::XRef>) -> DocumentPermissions {
        // Similar logic for pdf-rs
        if !file.is_encrypted() {
            DocumentPermissions {
                can_print: true,
                can_modify: true,
                can_copy: true,
                can_add_notes: true,
                can_fill_forms: true,
                can_extract_for_accessibility: true,
                can_assemble: true,
                can_print_high_quality: true,
            }
        } else {
            DocumentPermissions {
                can_print: false,
                can_modify: false,
                can_copy: false,
                can_add_notes: false,
                can_fill_forms: false,
                can_extract_for_accessibility: true,
                can_assemble: false,
                can_print_high_quality: false,
            }
        }
    }
    
    // Similar placeholder implementations for pdf-rs
    
    fn get_pdfrs_page_size(&self, page_num: u32) -> PDFResult<PageSize> {
        Ok(PageSize {
            width: 612.0,
            height: 792.0,
            rotation: 0,
        })
    }
    
    async fn render_pdfrs_page(&self, page_num: u32, scale: f32) -> PDFResult<RenderedPage> {
        Err(PDFError::UnsupportedFeature { 
            feature: "pdf-rs rendering not implemented".to_string() 
        })
    }
    
    #[cfg(feature = "pdf-rs-parser")]
    fn extract_pdfrs_text(&self, page_num: u32) -> PDFResult<String> {
        let file = self.pdfrs_doc.as_ref()
            .ok_or_else(|| PDFError::ConfigError("pdf-rs document not loaded".to_string()))?;

        if page_num == 0 || page_num > file.num_pages() as u32 {
            return Err(PDFError::InvalidPageNumber {
                page: page_num,
                total_pages: file.num_pages() as u32
            });
        }

        // Get the page
        match file.get_page(page_num - 1) {
            Ok(page) => {
                // Extract text from page content
                // This is a simplified implementation
                // In practice, we'd need to parse the content streams and text objects
                let mut text = String::new();

                // Try to extract text from page resources
                if let Ok(contents) = page.contents() {
                    for content in contents {
                        // Parse content stream for text
                        // This is a very basic implementation
                        text.push_str(&format!("Content from page {} (pdf-rs)\n", page_num));
                    }
                }

                if text.is_empty() {
                    text = format!("Page {} content (pdf-rs parser)", page_num);
                }

                Ok(text)
            },
            Err(e) => Err(PDFError::ParseError(format!("Failed to get page {}: {}", page_num, e)))
        }
    }
    
    fn search_pdfrs_text(&self, query: &str) -> PDFResult<Vec<SearchResult>> {
        Ok(vec![])
    }
}
