{"version": 0, "next_id": 2, "reports": [{"id": 1, "suggestion_message": "\nTo solve this problem, you can try the following approaches:\n\n\n- Some affected dependencies have newer versions available.\nYou may want to consider updating them to a newer version to see if the issue has been fixed.\n\npdf v0.8.1 has the following newer versions available: 0.9.0\n\n- If the issue is not solved by updating the dependencies, a fix has to be\nimplemented by those dependencies. You can help with that by notifying the\nmaintainers of this problem (e.g. by creating a bug report) or by proposing a\nfix to the maintainers (e.g. by creating a pull request):\n\n  - pdf@0.8.1\n  - Repository: https://github.com/pdf-rs/pdf\n  - Detailed warning command: `cargo report future-incompatibilities --id 1 --package pdf@0.8.1`\n\n- If waiting for an upstream fix is not an option, you can use the `[patch]`\nsection in `Cargo.toml` to use your own version of the dependency. For more\ninformation, see:\nhttps://doc.rust-lang.org/cargo/reference/overriding-dependencies.html#the-patch-section\n", "per_package": {"pdf@0.8.1": "The package `pdf v0.8.1` currently triggers the following future incompatibility lints:\n> \u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:9:27\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub media_box: Option<Rect>,\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:10:26\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub crop_box: Option<Rect>,\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:11:26\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub trim_box: Option<Rect>,\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \n> \u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `Rect` is ambiguous\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:39:31\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.media_box = Some(Rect {\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mambiguous name\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in a future release!\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for more information, see issue #114095 <https://github.com/rust-lang/rust/issues/114095>\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: ambiguous because of multiple glob imports of a name in the same module\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:1:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::object::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `Rect` could also refer to the struct imported here\u001b[0m\n> \u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\pdf-0.8.1\\src\\build.rs:2:5\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n> \u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::content::*;\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding an explicit import of `Rect` to disambiguate\u001b[0m\n> \n"}}]}