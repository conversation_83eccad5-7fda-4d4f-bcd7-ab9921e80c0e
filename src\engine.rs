use crate::parser::{PDFParser, ParserType};
use crate::renderer::Renderer;
use crate::cache::DocumentCache;
use crate::types::*;
use crate::error::{PDFError, PDFResult, ErrorContext};
use crate::{pdf_error, handle_recoverable};

use std::sync::{Arc, Mutex};
use wasm_bindgen::prelude::*;
use wasm_bindgen_futures::JsFuture;
use web_sys::{console, Performance, Window};

/// Main PDF rendering engine with hybrid parser support
pub struct PDFEngine {
    /// Primary parser (PDFium)
    primary_parser: Option<PDFParser>,
    /// Fallback parser (lopdf/pdf-rs)
    fallback_parser: Option<PDFParser>,
    /// Current active parser
    active_parser: Option<ParserType>,
    /// Renderer for page output
    renderer: Arc<Mutex<Renderer>>,
    /// Document cache
    cache: Arc<Mutex<DocumentCache>>,
    /// Engine configuration
    config: EngineConfig,
    /// Current document info
    document_info: Option<DocumentInfo>,
    /// Performance monitoring
    performance: Option<Performance>,
}

impl PDFEngine {
    /// Create a new PDF engine instance
    pub fn new() -> PDFResult<Self> {
        let config = EngineConfig::default();
        Self::with_config(config)
    }
    
    /// Create PDF engine with custom configuration
    pub fn with_config(config: EngineConfig) -> PDFResult<Self> {
        let context = ErrorContext::new("engine_initialization");
        
        // Initialize performance monitoring
        let performance = web_sys::window()
            .and_then(|w| w.performance());
        
        // Initialize cache
        let cache = Arc::new(Mutex::new(
            DocumentCache::new(config.cache_size_mb, config.max_cached_pages)
                .map_err(|e| pdf_error!(PDFError::ConfigError(e.to_string()), context))?
        ));
        
        // Initialize renderer
        let renderer = Arc::new(Mutex::new(
            Renderer::new(config.render_backend, config.enable_webgl)
                .map_err(|e| pdf_error!(PDFError::ConfigError(e.to_string()), context))?
        ));
        
        // Initialize parsers
        let primary_parser = Self::init_primary_parser(&config)?;
        let fallback_parser = Self::init_fallback_parser(&config)?;
        
        console::log_1(&"PDF Engine initialized successfully".into());
        
        Ok(PDFEngine {
            primary_parser,
            fallback_parser,
            active_parser: None,
            renderer,
            cache,
            config,
            document_info: None,
            performance,
        })
    }
    
    /// Initialize primary parser (PDFium)
    fn init_primary_parser(config: &EngineConfig) -> PDFResult<Option<PDFParser>> {
        if config.parser_backend == ParserBackend::PDFium {
            match PDFParser::new(ParserType::PDFium) {
                Ok(parser) => {
                    console::log_1(&"PDFium parser initialized".into());
                    Ok(Some(parser))
                },
                Err(e) => {
                    console::warn_1(&format!("Failed to initialize PDFium: {}", e).into());
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Initialize fallback parser (lopdf or pdf-rs)
    fn init_fallback_parser(config: &EngineConfig) -> PDFResult<Option<PDFParser>> {
        let parser_type = match config.parser_backend {
            ParserBackend::Lopdf => ParserType::Lopdf,
            ParserBackend::PdfRs => ParserType::PdfRs,
            _ => ParserType::Lopdf, // Default fallback
        };
        
        match PDFParser::new(parser_type) {
            Ok(parser) => {
                console::log_1(&format!("Fallback parser ({:?}) initialized", parser_type).into());
                Ok(Some(parser))
            },
            Err(e) => {
                console::warn_1(&format!("Failed to initialize fallback parser: {}", e).into());
                Ok(None)
            }
        }
    }
    
    /// Load a PDF document with hybrid parsing
    pub async fn load_document(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        let context = ErrorContext::new("load_document")
            .with_info(&format!("Document size: {} bytes", data.len()));
        
        let start_time = self.get_timestamp();
        
        // Clear previous document
        self.clear_cache();
        self.document_info = None;
        self.active_parser = None;
        
        // Try primary parser first
        let document_info = if let Some(ref mut parser) = self.primary_parser {
            match parser.load_document(data).await {
                Ok(info) => {
                    console::log_1(&"Document loaded with primary parser (PDFium)".into());
                    self.active_parser = Some(ParserType::PDFium);
                    info
                },
                Err(e) => {
                    console::warn_1(&format!("Primary parser failed: {}, trying fallback", e).into());
                    self.try_fallback_parser(data).await?
                }
            }
        } else {
            // No primary parser, use fallback
            self.try_fallback_parser(data).await?
        };
        
        let load_time = self.get_timestamp() - start_time;
        console::log_1(&format!("Document loaded in {:.2}ms", load_time).into());
        
        self.document_info = Some(document_info.clone());
        Ok(document_info)
    }
    
    /// Try fallback parser
    async fn try_fallback_parser(&mut self, data: &[u8]) -> PDFResult<DocumentInfo> {
        if let Some(ref mut parser) = self.fallback_parser {
            match parser.load_document(data).await {
                Ok(info) => {
                    console::log_1(&"Document loaded with fallback parser".into());
                    self.active_parser = Some(parser.get_type());
                    Ok(info)
                },
                Err(e) => {
                    Err(pdf_error!(
                        PDFError::ParseError(format!("All parsers failed. Last error: {}", e)),
                        ErrorContext::new("fallback_parser")
                    ))
                }
            }
        } else {
            Err(pdf_error!(
                PDFError::ParseError("No parsers available".to_string()),
                ErrorContext::new("fallback_parser")
            ))
        }
    }
    
    /// Get the number of pages in the document
    pub fn get_page_count(&self) -> u32 {
        self.document_info
            .as_ref()
            .map(|info| info.page_count())
            .unwrap_or(0)
    }
    
    /// Get page size information
    pub fn get_page_size(&self, page_num: u32) -> PDFResult<PageSize> {
        self.validate_page_number(page_num)?;
        
        let active_parser = self.get_active_parser()?;
        active_parser.get_page_size(page_num)
    }
    
    /// Render a page to canvas
    pub async fn render_page(&mut self, page_num: u32, scale: f32, canvas_id: &str) -> PDFResult<()> {
        let context = ErrorContext::new("render_page")
            .with_page(page_num)
            .with_info(&format!("scale: {}, canvas: {}", scale, canvas_id));
        
        self.validate_page_number(page_num)?;
        
        let start_time = self.get_timestamp();
        
        // Check cache first
        if let Some(cached_page) = self.get_cached_page(page_num, scale) {
            console::log_1(&format!("Using cached page {}", page_num).into());
            return self.render_cached_page(&cached_page, canvas_id).await;
        }
        
        // Render new page
        let active_parser = self.get_active_parser()?;
        let page_data = active_parser.render_page(page_num, scale).await
            .map_err(|e| pdf_error!(
                PDFError::RenderError { page: page_num, reason: e.to_string() },
                context
            ))?;
        
        // Cache the rendered page
        self.cache_page(page_num, scale, page_data.clone())?;
        
        // Render to canvas
        let renderer = self.renderer.lock().unwrap();
        renderer.render_to_canvas(&page_data, canvas_id).await
            .map_err(|e| pdf_error!(
                PDFError::RenderError { page: page_num, reason: e.to_string() },
                context
            ))?;
        
        let render_time = self.get_timestamp() - start_time;
        console::log_1(&format!("Page {} rendered in {:.2}ms", page_num, render_time).into());
        
        Ok(())
    }
    
    /// Extract text from a page
    pub fn extract_text(&self, page_num: u32) -> PDFResult<String> {
        self.validate_page_number(page_num)?;
        
        let active_parser = self.get_active_parser()?;
        active_parser.extract_text(page_num)
    }
    
    /// Search for text in the document
    pub fn search_text(&self, query: &str) -> PDFResult<Vec<SearchResult>> {
        let active_parser = self.get_active_parser()?;
        active_parser.search_text(query)
    }
    
    /// Clear the document cache
    pub fn clear_cache(&mut self) {
        if let Ok(mut cache) = self.cache.lock() {
            cache.clear();
            console::log_1(&"Cache cleared".into());
        }
    }
    
    /// Get memory usage statistics
    pub fn get_memory_usage(&self) -> MemoryUsage {
        if let Ok(cache) = self.cache.lock() {
            cache.get_memory_usage()
        } else {
            MemoryUsage {
                total_allocated: 0,
                cache_size: 0,
                active_pages: 0,
                cached_pages: 0,
            }
        }
    }
    
    // Helper methods
    
    fn validate_page_number(&self, page_num: u32) -> PDFResult<()> {
        let page_count = self.get_page_count();
        if page_num == 0 || page_num > page_count {
            return Err(PDFError::InvalidPageNumber {
                page: page_num,
                total_pages: page_count,
            });
        }
        Ok(())
    }
    
    fn get_active_parser(&self) -> PDFResult<&PDFParser> {
        match self.active_parser {
            Some(ParserType::PDFium) => {
                self.primary_parser.as_ref()
                    .ok_or_else(|| PDFError::ConfigError("PDFium parser not available".to_string()))
            },
            Some(_) => {
                self.fallback_parser.as_ref()
                    .ok_or_else(|| PDFError::ConfigError("Fallback parser not available".to_string()))
            },
            None => Err(PDFError::ConfigError("No document loaded".to_string())),
        }
    }
    
    fn get_cached_page(&self, page_num: u32, scale: f32) -> Option<RenderedPage> {
        if let Ok(mut cache) = self.cache.lock() {
            cache.get_page(page_num, scale)
        } else {
            None
        }
    }
    
    fn cache_page(&self, page_num: u32, scale: f32, page_data: RenderedPage) -> PDFResult<()> {
        if let Ok(mut cache) = self.cache.lock() {
            cache.store_page(page_num, scale, page_data)
                .map_err(|e| PDFError::CacheError { operation: e.to_string() })
        } else {
            Err(PDFError::ThreadError("Failed to acquire cache lock".to_string()))
        }
    }
    
    async fn render_cached_page(&self, page_data: &RenderedPage, canvas_id: &str) -> PDFResult<()> {
        let renderer = self.renderer.lock().unwrap();
        renderer.render_to_canvas(page_data, canvas_id).await
            .map_err(|e| PDFError::RenderError {
                page: page_data.page_num,
                reason: e.to_string(),
            })
    }
    
    fn get_timestamp(&self) -> f64 {
        self.performance
            .as_ref()
            .map(|p| p.now())
            .unwrap_or_else(|| js_sys::Date::now())
    }
}
