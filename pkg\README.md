# PDF Rendering Engine

A high-performance PDF rendering engine built with Rust and WebAssembly.

## Features

- 🚀 High-performance PDF parsing and rendering
- 🔧 Hybrid parser support (PDFium + pure Rust fallbacks)
- 🎨 Multiple rendering backends (Canvas2D, WebGL)
- 💾 Intelligent caching system
- 🔍 Text extraction and search
- 📱 Cross-platform support (Web, Node.js)

## Installation

```bash
npm install pdf-render-engine
```

## Usage

### Web (ES Modules)

```javascript
import init, { WasmPDFEngine } from 'pdf-render-engine/web/pdf_render_engine.js';

async function loadPDF() {
    await init();
    
    const engine = new WasmPDFEngine();
    const pdfData = new Uint8Array(/* your PDF data */);
    
    const docInfo = await engine.load_document(pdfData);
    console.log('Pages:', docInfo.page_count);
    
    await engine.render_page(1, 1.0, 'canvas-id');
}
```

### Bundler (Webpack, Vite, etc.)

```javascript
import init, { WasmPDFEngine } from 'pdf-render-engine';

// Same usage as above
```

### Node.js

```javascript
const { WasmPDFEngine } = require('pdf-render-engine/nodejs/pdf_render_engine.js');

// Node.js usage (without canvas rendering)
```

## API Reference

### WasmPDFEngine

#### Methods

- `new WasmPDFEngine()` - Create a new PDF engine instance
- `load_document(data: Uint8Array)` - Load a PDF document
- `get_page_count()` - Get the number of pages
- `render_page(pageNum: number, scale: number, canvasId: string)` - Render a page
- `extract_text(pageNum: number)` - Extract text from a page
- `search_text(query: string)` - Search for text in the document

## Browser Support

- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## License

MIT
