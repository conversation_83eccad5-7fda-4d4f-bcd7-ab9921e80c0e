use wasm_bindgen::prelude::*;

// Import the `console.log` function from the `console` module
#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
}

// Define a macro for easier console logging
macro_rules! console_log {
    ($($t:tt)*) => (log(&format_args!($($t)*).to_string()))
}

// Re-export main modules
pub mod engine;
pub mod parser;
pub mod renderer;
pub mod cache;
pub mod types;
pub mod utils;
pub mod error;

// WebAssembly exports
use engine::PDFEngine;
use types::*;
use error::PDFError;

// Initialize the PDF engine for WebAssembly
#[wasm_bindgen(start)]
pub fn init() {
    // Set up panic hook for better error messages
    #[cfg(feature = "console_error_panic_hook")]
    console_error_panic_hook::set_once();
    
    // Initialize logging
    #[cfg(feature = "debug-logging")]
    wasm_logger::init(wasm_logger::Config::default());
    
    console_log!("PDF Rendering Engine initialized");
}

// Main WebAssembly interface
#[wasm_bindgen]
pub struct WasmPDFEngine {
    engine: PDFEngine,
}

#[wasm_bindgen]
impl WasmPDFEngine {
    #[wasm_bindgen(constructor)]
    pub fn new() -> Result<WasmPDFEngine, JsValue> {
        console_log!("Creating new PDF engine instance");
        
        let engine = PDFEngine::new().map_err(|e| {
            JsValue::from_str(&format!("Failed to create PDF engine: {}", e))
        })?;
        
        Ok(WasmPDFEngine { engine })
    }
    
    #[wasm_bindgen]
    pub async fn load_document(&mut self, data: &[u8]) -> Result<JsValue, JsValue> {
        console_log!("Loading PDF document, size: {} bytes", data.len());
        
        let document_info = self.engine.load_document(data).await.map_err(|e| {
            JsValue::from_str(&format!("Failed to load document: {}", e))
        })?;
        
        // Convert to JavaScript object
        serde_wasm_bindgen::to_value(&document_info).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize document info: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn get_page_count(&self) -> u32 {
        self.engine.get_page_count()
    }
    
    #[wasm_bindgen]
    pub async fn render_page(&mut self, page_num: u32, scale: f32, canvas_id: &str) -> Result<(), JsValue> {
        console_log!("Rendering page {} at scale {}", page_num, scale);
        
        self.engine.render_page(page_num, scale, canvas_id).await.map_err(|e| {
            JsValue::from_str(&format!("Failed to render page: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn get_page_size(&self, page_num: u32) -> Result<JsValue, JsValue> {
        let size = self.engine.get_page_size(page_num).map_err(|e| {
            JsValue::from_str(&format!("Failed to get page size: {}", e))
        })?;
        
        serde_wasm_bindgen::to_value(&size).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize page size: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn extract_text(&self, page_num: u32) -> Result<String, JsValue> {
        self.engine.extract_text(page_num).map_err(|e| {
            JsValue::from_str(&format!("Failed to extract text: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn search_text(&self, query: &str) -> Result<JsValue, JsValue> {
        let results = self.engine.search_text(query).map_err(|e| {
            JsValue::from_str(&format!("Failed to search text: {}", e))
        })?;
        
        serde_wasm_bindgen::to_value(&results).map_err(|e| {
            JsValue::from_str(&format!("Failed to serialize search results: {}", e))
        })
    }
    
    #[wasm_bindgen]
    pub fn clear_cache(&mut self) {
        self.engine.clear_cache();
        console_log!("Cache cleared");
    }
    
    #[wasm_bindgen]
    pub fn get_memory_usage(&self) -> JsValue {
        let usage = self.engine.get_memory_usage();
        serde_wasm_bindgen::to_value(&usage).unwrap_or(JsValue::NULL)
    }
}

// Utility functions for JavaScript integration
#[wasm_bindgen]
pub fn set_log_level(level: &str) {
    console_log!("Setting log level to: {}", level);
    // Implementation for log level setting
}

#[wasm_bindgen]
pub fn get_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

#[wasm_bindgen]
pub fn get_supported_features() -> JsValue {
    let features = vec![
        "pdf_parsing",
        "page_rendering", 
        "text_extraction",
        "text_search",
        "caching",
        "webgl_acceleration"
    ];
    
    serde_wasm_bindgen::to_value(&features).unwrap_or(JsValue::NULL)
}
